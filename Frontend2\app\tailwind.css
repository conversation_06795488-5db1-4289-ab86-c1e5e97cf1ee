@import url('https://fonts.googleapis.com/css2?family=Merriweather:wght@300;400;700;900&family=Source+Sans+Pro:wght@300;400;600;700&family=Montserrat:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body {
  @apply bg-white dark:bg-gray-950;

  @media (prefers-color-scheme: light) {
    color-scheme: light;
  }
}

/* Hide scrollbar for popup forms */
.popup-scroll::-webkit-scrollbar {
  display: none;
}

.popup-scroll {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Glassmorphic enhancements */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-gold-tint {
  background: linear-gradient(135deg, rgba(184, 134, 11, 0.1), rgba(255, 255, 255, 0.05), rgba(59, 130, 246, 0.1));
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
}

/* Enhanced focus states for glassmorphic inputs */
.glass-input:focus {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(184, 134, 11, 0.6);
  box-shadow: 0 0 0 2px rgba(184, 134, 11, 0.3), 0 4px 16px rgba(184, 134, 11, 0.2);
}
