{"version": 3, "file": "CSSRulePlugin.min.js", "sources": ["../src/CSSRulePlugin.js"], "sourcesContent": ["/*!\n * CSSRulePlugin 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, _win, _doc, CSSPlugin,\n\t_windowExists = () => typeof(window) !== \"undefined\",\n\t_getGSAP = () => gsap || (_windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_checkRegister = () => {\n\t\tif (!_coreInitted) {\n\t\t\t_initCore();\n\t\t\tif (!CSSPlugin) {\n\t\t\t\tconsole.warn(\"Please gsap.registerPlugin(CSSPlugin, CSSRulePlugin)\");\n\t\t\t}\n\t\t}\n\t\treturn _coreInitted;\n\t},\n\t_initCore = core => {\n\t\tgsap = core || _getGSAP();\n\t\tif (_windowExists()) {\n\t\t\t_win = window;\n\t\t\t_doc = document;\n\t\t}\n\t\tif (gsap) {\n\t\t\tCSSPlugin = gsap.plugins.css;\n\t\t\tif (CSSPlugin) {\n\t\t\t\t_coreInitted = 1;\n\t\t\t}\n\t\t}\n\t};\n\n\nexport const CSSRulePlugin = {\n\tversion: \"3.13.0\",\n\tname: \"cssRule\",\n\tinit(target, value, tween, index, targets) {\n\t\tif (!_checkRegister() || typeof(target.cssText) === \"undefined\") {\n\t\t\treturn false;\n\t\t}\n\t\tlet div = target._gsProxy = target._gsProxy || _doc.createElement(\"div\");\n\t\tthis.ss = target;\n\t\tthis.style = div.style;\n\t\tdiv.style.cssText = target.cssText;\n\t\tCSSPlugin.prototype.init.call(this, div, value, tween, index, targets); //we just offload all the work to the regular CSSPlugin and then copy the cssText back over to the rule in the render() method. This allows us to have all of the updates to CSSPlugin automatically flow through to CSSRulePlugin instead of having to maintain both\n\t},\n\trender(ratio, data) {\n\t\tlet pt = data._pt,\n\t\t\tstyle = data.style,\n\t\t\tss = data.ss,\n\t\t\ti;\n\t\twhile (pt) {\n\t\t\tpt.r(ratio, pt.d);\n\t\t\tpt = pt._next;\n\t\t}\n\t\ti = style.length;\n\t\twhile (--i > -1) {\n\t\t\tss[style[i]] = style[style[i]];\n\t\t}\n\t},\n\tgetRule(selector) {\n\t\t_checkRegister();\n\t\tlet ruleProp = _doc.all ? \"rules\" : \"cssRules\",\n\t\t\tstyleSheets = _doc.styleSheets,\n\t\t\ti = styleSheets.length,\n\t\t\tpseudo = (selector.charAt(0) === \":\"),\n\t\t\tj, curSS, cs, a;\n\t\tselector = (pseudo ? \"\" : \",\") + selector.split(\"::\").join(\":\").toLowerCase() + \",\"; //note: old versions of IE report tag name selectors as upper case, so we just change everything to lowercase.\n\t\tif (pseudo) {\n\t\t\ta = [];\n\t\t}\n\t\twhile (i--) {\n\t\t\t//Firefox may throw insecure operation errors when css is loaded from other domains, so try/catch.\n\t\t\ttry {\n\t\t\t\tcurSS = styleSheets[i][ruleProp];\n\t\t\t\tif (!curSS) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\t\t\t\tj = curSS.length;\n\t\t\t} catch (e) {\n\t\t\t\tconsole.warn(e);\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\twhile (--j > -1) {\n\t\t\t\tcs = curSS[j];\n\t\t\t\tif (cs.selectorText && (\",\" + cs.selectorText.split(\"::\").join(\":\").toLowerCase() + \",\").indexOf(selector) !== -1) { //note: IE adds an extra \":\" to pseudo selectors, so .myClass:after becomes .myClass::after, so we need to strip the extra one out.\n\t\t\t\t\tif (pseudo) {\n\t\t\t\t\t\ta.push(cs.style);\n\t\t\t\t\t} else {\n\t\t\t\t\t\treturn cs.style;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn a;\n\t},\n\tregister: _initCore\n};\n\n_getGSAP() && gsap.registerPlugin(CSSRulePlugin);\n\nexport { CSSRulePlugin as default };"], "names": ["_windowExists", "window", "_getGSAP", "gsap", "registerPlugin", "_checkRegister", "_coreInitted", "_initCore", "CSSPlugin", "console", "warn", "_doc", "core", "document", "plugins", "css", "CSSRulePlugin", "version", "name", "init", "target", "value", "tween", "index", "targets", "cssText", "div", "_gsProxy", "createElement", "ss", "style", "prototype", "call", "this", "render", "ratio", "data", "i", "pt", "_pt", "r", "d", "_next", "length", "getRule", "selector", "j", "curSS", "cs", "a", "ruleProp", "all", "styleSheets", "pseudo", "char<PERSON>t", "split", "join", "toLowerCase", "e", "selectorText", "indexOf", "push", "register"], "mappings": ";;;;;;;;;6MAWiB,SAAhBA,UAAyC,oBAAZC,OAClB,SAAXC,WAAiBC,GAASH,MAAoBG,EAAOF,OAAOE,OAASA,EAAKC,gBAAkBD,EAC3E,SAAjBE,WACMC,IACJC,IACKC,GACJC,QAAQC,KAAK,yDAGRJ,MAVLH,EAAMG,EAAoBK,EAAMH,EAYnCD,EAAY,SAAZA,UAAYK,GACXT,EAAOS,GAAQV,IACXF,MAEHW,EAAOE,UAEJV,IACHK,EAAYL,EAAKW,QAAQC,OAExBT,EAAe,IAMNU,EAAgB,CAC5BC,QAAS,SACTC,KAAM,UACNC,mBAAKC,EAAQC,EAAOC,EAAOC,EAAOC,OAC5BnB,UAA+C,IAApBe,EAAOK,eAC/B,MAEJC,EAAMN,EAAOO,SAAWP,EAAOO,UAAYhB,EAAKiB,cAAc,YAC7DC,GAAKT,OACLU,MAAQJ,EAAII,MACjBJ,EAAII,MAAML,QAAUL,EAAOK,QAC3BjB,EAAUuB,UAAUZ,KAAKa,KAAKC,KAAMP,EAAKL,EAAOC,EAAOC,EAAOC,IAE/DU,uBAAOC,EAAOC,WAIZC,EAHGC,EAAKF,EAAKG,IACbT,EAAQM,EAAKN,MACbD,EAAKO,EAAKP,GAEJS,GACNA,EAAGE,EAAEL,EAAOG,EAAGG,GACfH,EAAKA,EAAGI,UAETL,EAAIP,EAAMa,QACI,IAALN,GACRR,EAAGC,EAAMO,IAAMP,EAAMA,EAAMO,KAG7BO,yBAAQC,GACPxC,QAKCyC,EAAGC,EAAOC,EAAIC,EAJXC,EAAWvC,EAAKwC,IAAM,QAAU,WACnCC,EAAczC,EAAKyC,YACnBf,EAAIe,EAAYT,OAChBU,EAAiC,MAAvBR,EAASS,OAAO,OAE3BT,GAAYQ,EAAS,GAAK,KAAOR,EAASU,MAAM,MAAMC,KAAK,KAAKC,cAAgB,IAC5EJ,IACHJ,EAAI,IAEEZ,KAAK,UAGVU,EAAQK,EAAYf,GAAGa,aAIvBJ,EAAIC,EAAMJ,OACT,MAAOe,GACRjD,QAAQC,KAAKgD,kBAGA,IAALZ,OACRE,EAAKD,EAAMD,IACJa,eAAyG,KAAxF,IAAMX,EAAGW,aAAaJ,MAAM,MAAMC,KAAK,KAAKC,cAAgB,KAAKG,QAAQf,GAAkB,KAC9GQ,SAGIL,EAAGlB,MAFVmB,EAAEY,KAAKb,EAAGlB,eAOPmB,GAERa,SAAUvD,GAGXL,KAAcC,EAAKC,eAAeY"}