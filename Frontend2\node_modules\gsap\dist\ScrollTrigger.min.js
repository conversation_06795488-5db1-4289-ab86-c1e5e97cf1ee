/*!
 * ScrollTrigger 3.13.0
 * https://gsap.com
 * 
 * @license Copyright 2025, GreenSock. All rights reserved.
 * Subject to the terms at https://gsap.com/standard-license.
 * @author: <PERSON>, <EMAIL>
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,function(e){"use strict";function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function r(){return Se||"undefined"!=typeof window&&(Se=window.gsap)&&Se.registerPlugin&&Se}function z(e,t){return~Ye.indexOf(e)&&Ye[Ye.indexOf(e)+1][t]}function A(e){return!!~t.indexOf(e)}function B(e,t,r,n,i){return e.addEventListener(t,r,{passive:!1!==n,capture:!!i})}function C(e,t,r,n){return e.removeEventListener(t,r,!!n)}function F(){return Re&&Re.isPressed||ze.cache++}function G(r,n){function ed(e){if(e||0===e){i&&(Te.history.scrollRestoration="manual");var t=Re&&Re.isPressed;e=ed.v=Math.round(e)||(Re&&Re.iOS?1:0),r(e),ed.cacheID=ze.cache,t&&o("ss",e)}else(n||ze.cache!==ed.cacheID||o("ref"))&&(ed.cacheID=ze.cache,ed.v=r());return ed.v+ed.offset}return ed.offset=0,r&&ed}function J(e,t){return(t&&t._ctx&&t._ctx.selector||Se.utils.toArray)(e)[0]||("string"==typeof e&&!1!==Se.config().nullTargetWarn?console.warn("Element not found:",e):null)}function L(t,e){var r=e.s,n=e.sc;A(t)&&(t=Ce.scrollingElement||Me);var i=ze.indexOf(t),o=n===qe.sc?1:2;~i||(i=ze.push(t)-1),ze[i+o]||B(t,"scroll",F);var a=ze[i+o],s=a||(ze[i+o]=G(z(t,r),!0)||(A(t)?n:G(function(e){return arguments.length?t[r]=e:t[r]})));return s.target=t,a||(s.smooth="smooth"===Se.getProperty(t,"scrollBehavior")),s}function M(e,t,i){function Gd(e,t){var r=Le();t||n<r-s?(a=o,o=e,l=s,s=r):i?o+=e:o=a+(e-a)/(r-l)*(s-l)}var o=e,a=e,s=Le(),l=s,n=t||50,c=Math.max(500,3*n);return{update:Gd,reset:function reset(){a=o=i?0:o,l=s=0},getVelocity:function getVelocity(e){var t=l,r=a,n=Le();return!e&&0!==e||e===o||Gd(e),s===l||c<n-l?0:(o+(i?r:-r))/((i?n:s)-t)*1e3}}}function N(e,t){return t&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e}function O(e){var t=Math.max.apply(Math,e),r=Math.min.apply(Math,e);return Math.abs(t)>=Math.abs(r)?t:r}function P(){(Ae=Se.core.globals().ScrollTrigger)&&Ae.core&&function _integrate(){var e=Ae.core,r=e.bridge||{},t=e._scrollers,n=e._proxies;t.push.apply(t,ze),n.push.apply(n,Ye),ze=t,Ye=n,o=function _bridge(e,t){return r[e](t)}}()}function Q(e){return Se=e||r(),!ke&&Se&&"undefined"!=typeof document&&document.body&&(Te=window,Me=(Ce=document).documentElement,Ee=Ce.body,t=[Te,Ce,Me,Ee],Se.utils.clamp,Be=Se.core.context||function(){},Oe="onpointerenter"in Ee?"pointer":"mouse",Pe=E.isTouch=Te.matchMedia&&Te.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in Te||0<navigator.maxTouchPoints||0<navigator.msMaxTouchPoints?2:0,De=E.eventTypes=("ontouchstart"in Me?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in Me?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout(function(){return i=0},500),P(),ke=1),ke}var Se,ke,Te,Ce,Me,Ee,Pe,Oe,Ae,t,Re,De,Be,i=1,Ie=[],ze=[],Ye=[],Le=Date.now,o=function _bridge(e,t){return t},n="scrollLeft",a="scrollTop",He={s:n,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:G(function(e){return arguments.length?Te.scrollTo(e,qe.sc()):Te.pageXOffset||Ce[n]||Me[n]||Ee[n]||0})},qe={s:a,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:He,sc:G(function(e){return arguments.length?Te.scrollTo(He.sc(),e):Te.pageYOffset||Ce[a]||Me[a]||Ee[a]||0})};He.op=qe,ze.cache=0;var E=(Observer.prototype.init=function init(e){ke||Q(Se)||console.warn("Please gsap.registerPlugin(Observer)"),Ae||P();var i=e.tolerance,a=e.dragMinimum,t=e.type,o=e.target,r=e.lineHeight,n=e.debounce,s=e.preventDefault,l=e.onStop,c=e.onStopDelay,u=e.ignore,f=e.wheelSpeed,d=e.event,p=e.onDragStart,g=e.onDragEnd,h=e.onDrag,v=e.onPress,b=e.onRelease,m=e.onRight,y=e.onLeft,x=e.onUp,_=e.onDown,w=e.onChangeX,S=e.onChangeY,k=e.onChange,T=e.onToggleX,E=e.onToggleY,R=e.onHover,D=e.onHoverEnd,I=e.onMove,z=e.ignoreCheck,Y=e.isNormalizer,H=e.onGestureStart,q=e.onGestureEnd,X=e.onWheel,U=e.onEnable,W=e.onDisable,G=e.onClick,V=e.scrollSpeed,K=e.capture,j=e.allowClicks,Z=e.lockAxis,$=e.onLockAxis;function gf(){return xe=Le()}function hf(e,t){return(se.event=e)&&u&&function _isWithin(e,t){for(var r=t.length;r--;)if(t[r]===e||t[r].contains(e))return!0;return!1}(e.target,u)||t&&he&&"touch"!==e.pointerType||z&&z(e,t)}function kf(){var e=se.deltaX=O(me),t=se.deltaY=O(ye),r=Math.abs(e)>=i,n=Math.abs(t)>=i;k&&(r||n)&&k(se,e,t,me,ye),r&&(m&&0<se.deltaX&&m(se),y&&se.deltaX<0&&y(se),w&&w(se),T&&se.deltaX<0!=le<0&&T(se),le=se.deltaX,me[0]=me[1]=me[2]=0),n&&(_&&0<se.deltaY&&_(se),x&&se.deltaY<0&&x(se),S&&S(se),E&&se.deltaY<0!=ce<0&&E(se),ce=se.deltaY,ye[0]=ye[1]=ye[2]=0),(ne||re)&&(I&&I(se),re&&(p&&1===re&&p(se),h&&h(se),re=0),ne=!1),oe&&!(oe=!1)&&$&&$(se),ie&&(X(se),ie=!1),ee=0}function lf(e,t,r){me[r]+=e,ye[r]+=t,se._vx.update(e),se._vy.update(t),n?ee=ee||requestAnimationFrame(kf):kf()}function mf(e,t){Z&&!ae&&(se.axis=ae=Math.abs(e)>Math.abs(t)?"x":"y",oe=!0),"y"!==ae&&(me[2]+=e,se._vx.update(e,!0)),"x"!==ae&&(ye[2]+=t,se._vy.update(t,!0)),n?ee=ee||requestAnimationFrame(kf):kf()}function nf(e){if(!hf(e,1)){var t=(e=N(e,s)).clientX,r=e.clientY,n=t-se.x,i=r-se.y,o=se.isDragging;se.x=t,se.y=r,(o||(n||i)&&(Math.abs(se.startX-t)>=a||Math.abs(se.startY-r)>=a))&&(re=o?2:1,o||(se.isDragging=!0),mf(n,i))}}function qf(e){return e.touches&&1<e.touches.length&&(se.isGesturing=!0)&&H(e,se.isDragging)}function rf(){return(se.isGesturing=!1)||q(se)}function sf(e){if(!hf(e)){var t=fe(),r=de();lf((t-pe)*V,(r-ge)*V,1),pe=t,ge=r,l&&te.restart(!0)}}function tf(e){if(!hf(e)){e=N(e,s),X&&(ie=!0);var t=(1===e.deltaMode?r:2===e.deltaMode?Te.innerHeight:1)*f;lf(e.deltaX*t,e.deltaY*t,0),l&&!Y&&te.restart(!0)}}function uf(e){if(!hf(e)){var t=e.clientX,r=e.clientY,n=t-se.x,i=r-se.y;se.x=t,se.y=r,ne=!0,l&&te.restart(!0),(n||i)&&mf(n,i)}}function vf(e){se.event=e,R(se)}function wf(e){se.event=e,D(se)}function xf(e){return hf(e)||N(e,s)&&G(se)}this.target=o=J(o)||Me,this.vars=e,u=u&&Se.utils.toArray(u),i=i||1e-9,a=a||0,f=f||1,V=V||1,t=t||"wheel,touch,pointer",n=!1!==n,r=r||parseFloat(Te.getComputedStyle(Ee).lineHeight)||22;var ee,te,re,ne,ie,oe,ae,se=this,le=0,ce=0,ue=e.passive||!s&&!1!==e.passive,fe=L(o,He),de=L(o,qe),pe=fe(),ge=de(),he=~t.indexOf("touch")&&!~t.indexOf("pointer")&&"pointerdown"===De[0],ve=A(o),be=o.ownerDocument||Ce,me=[0,0,0],ye=[0,0,0],xe=0,_e=se.onPress=function(e){hf(e,1)||e&&e.button||(se.axis=ae=null,te.pause(),se.isPressed=!0,e=N(e),le=ce=0,se.startX=se.x=e.clientX,se.startY=se.y=e.clientY,se._vx.reset(),se._vy.reset(),B(Y?o:be,De[1],nf,ue,!0),se.deltaX=se.deltaY=0,v&&v(se))},we=se.onRelease=function(t){if(!hf(t,1)){C(Y?o:be,De[1],nf,!0);var e=!isNaN(se.y-se.startY),r=se.isDragging,n=r&&(3<Math.abs(se.x-se.startX)||3<Math.abs(se.y-se.startY)),i=N(t);!n&&e&&(se._vx.reset(),se._vy.reset(),s&&j&&Se.delayedCall(.08,function(){if(300<Le()-xe&&!t.defaultPrevented)if(t.target.click)t.target.click();else if(be.createEvent){var e=be.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,Te,1,i.screenX,i.screenY,i.clientX,i.clientY,!1,!1,!1,!1,0,null),t.target.dispatchEvent(e)}})),se.isDragging=se.isGesturing=se.isPressed=!1,l&&r&&!Y&&te.restart(!0),re&&kf(),g&&r&&g(se),b&&b(se,n)}};te=se._dc=Se.delayedCall(c||.25,function onStopFunc(){se._vx.reset(),se._vy.reset(),te.pause(),l&&l(se)}).pause(),se.deltaX=se.deltaY=0,se._vx=M(0,50,!0),se._vy=M(0,50,!0),se.scrollX=fe,se.scrollY=de,se.isDragging=se.isGesturing=se.isPressed=!1,Be(this),se.enable=function(e){return se.isEnabled||(B(ve?be:o,"scroll",F),0<=t.indexOf("scroll")&&B(ve?be:o,"scroll",sf,ue,K),0<=t.indexOf("wheel")&&B(o,"wheel",tf,ue,K),(0<=t.indexOf("touch")&&Pe||0<=t.indexOf("pointer"))&&(B(o,De[0],_e,ue,K),B(be,De[2],we),B(be,De[3],we),j&&B(o,"click",gf,!0,!0),G&&B(o,"click",xf),H&&B(be,"gesturestart",qf),q&&B(be,"gestureend",rf),R&&B(o,Oe+"enter",vf),D&&B(o,Oe+"leave",wf),I&&B(o,Oe+"move",uf)),se.isEnabled=!0,se.isDragging=se.isGesturing=se.isPressed=ne=re=!1,se._vx.reset(),se._vy.reset(),pe=fe(),ge=de(),e&&e.type&&_e(e),U&&U(se)),se},se.disable=function(){se.isEnabled&&(Ie.filter(function(e){return e!==se&&A(e.target)}).length||C(ve?be:o,"scroll",F),se.isPressed&&(se._vx.reset(),se._vy.reset(),C(Y?o:be,De[1],nf,!0)),C(ve?be:o,"scroll",sf,K),C(o,"wheel",tf,K),C(o,De[0],_e,K),C(be,De[2],we),C(be,De[3],we),C(o,"click",gf,!0),C(o,"click",xf),C(be,"gesturestart",qf),C(be,"gestureend",rf),C(o,Oe+"enter",vf),C(o,Oe+"leave",wf),C(o,Oe+"move",uf),se.isEnabled=se.isPressed=se.isDragging=!1,W&&W(se))},se.kill=se.revert=function(){se.disable();var e=Ie.indexOf(se);0<=e&&Ie.splice(e,1),Re===se&&(Re=0)},Ie.push(se),Y&&A(o)&&(Re=se),se.enable(d)},function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),e}(Observer,[{key:"velocityX",get:function get(){return this._vx.getVelocity()}},{key:"velocityY",get:function get(){return this._vy.getVelocity()}}]),Observer);function Observer(e){this.init(e)}E.version="3.13.0",E.create=function(e){return new E(e)},E.register=Q,E.getAll=function(){return Ie.slice()},E.getById=function(t){return Ie.filter(function(e){return e.vars.id===t})[0]},r()&&Se.registerPlugin(E);function Da(e,t,r){var n=ct(e)&&("clamp("===e.substr(0,6)||-1<e.indexOf("max"));return(r["_"+t+"Clamp"]=n)?e.substr(6,e.length-7):e}function Ea(e,t){return!t||ct(e)&&"clamp("===e.substr(0,6)?e:"clamp("+e+")"}function Ga(){return Qe=1}function Ha(){return Qe=0}function Ia(e){return e}function Ja(e){return Math.round(1e5*e)/1e5||0}function Ka(){return"undefined"!=typeof window}function La(){return Ne||Ka()&&(Ne=window.gsap)&&Ne.registerPlugin&&Ne}function Ma(e){return!!~l.indexOf(e)}function Na(e){return("Height"===e?S:Xe["inner"+e])||Ue["client"+e]||We["client"+e]}function Oa(e){return z(e,"getBoundingClientRect")||(Ma(e)?function(){return Ot.width=Xe.innerWidth,Ot.height=S,Ot}:function(){return _t(e)})}function Ra(e,t){var r=t.s,n=t.d2,i=t.d,o=t.a;return Math.max(0,(r="scroll"+n)&&(o=z(e,r))?o()-Oa(e)()[i]:Ma(e)?(Ue[r]||We[r])-Na(n):e[r]-e["offset"+n])}function Sa(e,t){for(var r=0;r<g.length;r+=3)t&&!~t.indexOf(g[r+1])||e(g[r],g[r+1],g[r+2])}function Ua(e){return"function"==typeof e}function Va(e){return"number"==typeof e}function Wa(e){return"object"==typeof e}function Xa(e,t,r){return e&&e.progress(t?0:1)&&r&&e.pause()}function Ya(e,t){if(e.enabled){var r=e._ctx?e._ctx.add(function(){return t(e)}):t(e);r&&r.totalTime&&(e.callbackAnimation=r)}}function nb(e){return Xe.getComputedStyle(e)}function pb(e,t){for(var r in t)r in e||(e[r]=t[r]);return e}function rb(e,t){var r=t.d2;return e["offset"+r]||e["client"+r]||0}function sb(e){var t,r=[],n=e.labels,i=e.duration();for(t in n)r.push(n[t]/i);return r}function ub(i){var o=Ne.utils.snap(i),a=Array.isArray(i)&&i.slice(0).sort(function(e,t){return e-t});return a?function(e,t,r){var n;if(void 0===r&&(r=.001),!t)return o(e);if(0<t){for(e-=r,n=0;n<a.length;n++)if(a[n]>=e)return a[n];return a[n-1]}for(n=a.length,e+=r;n--;)if(a[n]<=e)return a[n];return a[0]}:function(e,t,r){void 0===r&&(r=.001);var n=o(e);return!t||Math.abs(n-e)<r||n-e<0==t<0?n:o(t<0?e-i:e+i)}}function wb(t,r,e,n){return e.split(",").forEach(function(e){return t(r,e,n)})}function xb(e,t,r,n,i){return e.addEventListener(t,r,{passive:!n,capture:!!i})}function yb(e,t,r,n){return e.removeEventListener(t,r,!!n)}function zb(e,t,r){(r=r&&r.wheelHandler)&&(e(t,"wheel",r),e(t,"touchmove",r))}function Db(e,t){if(ct(e)){var r=e.indexOf("="),n=~r?(e.charAt(r-1)+1)*parseFloat(e.substr(r+1)):0;~r&&(e.indexOf("%")>r&&(n*=t/100),e=e.substr(0,r-1)),e=n+(e in q?q[e]*t:~e.indexOf("%")?parseFloat(e)*t/100:parseFloat(e)||0)}return e}function Eb(e,t,r,n,i,o,a,s){var l=i.startColor,c=i.endColor,u=i.fontSize,f=i.indent,d=i.fontWeight,p=Fe.createElement("div"),g=Ma(r)||"fixed"===z(r,"pinType"),h=-1!==e.indexOf("scroller"),v=g?We:r,b=-1!==e.indexOf("start"),m=b?l:c,y="border-color:"+m+";font-size:"+u+";color:"+m+";font-weight:"+d+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return y+="position:"+((h||s)&&g?"fixed;":"absolute;"),!h&&!s&&g||(y+=(n===qe?I:Y)+":"+(o+parseFloat(f))+"px;"),a&&(y+="box-sizing:border-box;text-align:left;width:"+a.offsetWidth+"px;"),p._isStart=b,p.setAttribute("class","gsap-marker-"+e+(t?" marker-"+t:"")),p.style.cssText=y,p.innerText=t||0===t?e+"-"+t:e,v.children[0]?v.insertBefore(p,v.children[0]):v.appendChild(p),p._offset=p["offset"+n.op.d2],X(p,0,n,b),p}function Jb(){return 34<at()-st&&(T=T||requestAnimationFrame(Z))}function Kb(){v&&v.isPressed&&!(v.startX>We.clientWidth)||(ze.cache++,v?T=T||requestAnimationFrame(Z):Z(),st||V("scrollStart"),st=at())}function Lb(){y=Xe.innerWidth,m=Xe.innerHeight}function Mb(e){ze.cache++,!0!==e&&(Ke||h||Fe.fullscreenElement||Fe.webkitFullscreenElement||b&&y===Xe.innerWidth&&!(Math.abs(Xe.innerHeight-m)>.25*Xe.innerHeight))||c.restart(!0)}function Pb(){return yb(ne,"scrollEnd",Pb)||Mt(!0)}function Sb(e){for(var t=0;t<K.length;t+=5)(!e||K[t+4]&&K[t+4].query===e)&&(K[t].style.cssText=K[t+1],K[t].getBBox&&K[t].setAttribute("transform",K[t+2]||""),K[t+3].uncache=1)}function Tb(e,t){var r;for(je=0;je<kt.length;je++)!(r=kt[je])||t&&r._ctx!==t||(e?r.kill(1):r.revert(!0,!0));k=!0,t&&Sb(t),t||V("revert")}function Ub(e,t){ze.cache++,!t&&rt||ze.forEach(function(e){return Ua(e)&&e.cacheID++&&(e.rec=0)}),ct(e)&&(Xe.history.scrollRestoration=_=e)}function Zb(){We.appendChild(w),S=!v&&w.offsetHeight||Xe.innerHeight,We.removeChild(w)}function $b(t){return Je(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach(function(e){return e.style.display=t?"none":"block"})}function hc(e,t,r,n){if(!e._gsap.swappedIn){for(var i,o=$.length,a=t.style,s=e.style;o--;)a[i=$[o]]=r[i];a.position="absolute"===r.position?"absolute":"relative","inline"===r.display&&(a.display="inline-block"),s[Y]=s[I]="auto",a.flexBasis=r.flexBasis||"auto",a.overflow="visible",a.boxSizing="border-box",a[ft]=rb(e,He)+xt,a[dt]=rb(e,qe)+xt,a[bt]=s[mt]=s.top=s.left="0",Pt(n),s[ft]=s.maxWidth=r[ft],s[dt]=s.maxHeight=r[dt],s[bt]=r[bt],e.parentNode!==t&&(e.parentNode.insertBefore(t,e),t.appendChild(e)),e._gsap.swappedIn=!0}}function kc(e){for(var t=ee.length,r=e.style,n=[],i=0;i<t;i++)n.push(ee[i],r[ee[i]]);return n.t=e,n}function nc(e,t,r,n,i,o,a,s,l,c,u,f,d,p){Ua(e)&&(e=e(s)),ct(e)&&"max"===e.substr(0,3)&&(e=f+("="===e.charAt(4)?Db("0"+e.substr(3),r):0));var g,h,v,b=d?d.time():0;if(d&&d.seek(0),isNaN(e)||(e=+e),Va(e))d&&(e=Ne.utils.mapRange(d.scrollTrigger.start,d.scrollTrigger.end,0,f,e)),a&&X(a,r,n,!0);else{Ua(t)&&(t=t(s));var m,y,x,_,w=(e||"0").split(" ");v=J(t,s)||We,(m=_t(v)||{})&&(m.left||m.top)||"none"!==nb(v).display||(_=v.style.display,v.style.display="block",m=_t(v),_?v.style.display=_:v.style.removeProperty("display")),y=Db(w[0],m[n.d]),x=Db(w[1]||"0",r),e=m[n.p]-l[n.p]-c+y+i-x,a&&X(a,x,n,r-x<20||a._isStart&&20<x),r-=r-x}if(p&&(s[p]=e||-.001,e<0&&(e=0)),o){var S=e+r,k=o._isStart;g="scroll"+n.d2,X(o,S,n,k&&20<S||!k&&(u?Math.max(We[g],Ue[g]):o.parentNode[g])<=S+1),u&&(l=_t(a),u&&(o.style[n.op.p]=l[n.op.p]-n.op.m-o._offset+xt))}return d&&v&&(g=_t(v),d.seek(f),h=_t(v),d._caScrollDist=g[n.p]-h[n.p],e=e/d._caScrollDist*f),d&&d.seek(b),d?e:Math.round(e)}function pc(e,t,r,n){if(e.parentNode!==t){var i,o,a=e.style;if(t===We){for(i in e._stOrig=a.cssText,o=nb(e))+i||re.test(i)||!o[i]||"string"!=typeof a[i]||"0"===i||(a[i]=o[i]);a.top=r,a.left=n}else a.cssText=e._stOrig;Ne.core.getCache(e).uncache=1,t.appendChild(e)}}function qc(r,e,n){var i=e,o=i;return function(e){var t=Math.round(r());return t!==i&&t!==o&&3<Math.abs(t-i)&&3<Math.abs(t-o)&&(e=t,n&&n()),o=i,i=Math.round(e)}}function rc(e,t,r){var n={};n[t.p]="+="+r,Ne.set(e,n)}function sc(c,e){function Hk(e,t,r,n,i){var o=Hk.tween,a=t.onComplete,s={};r=r||u();var l=qc(u,r,function(){o.kill(),Hk.tween=0});return i=n&&i||0,n=n||e-r,o&&o.kill(),t[f]=e,t.inherit=!1,(t.modifiers=s)[f]=function(){return l(r+n*o.ratio+i*o.ratio*o.ratio)},t.onUpdate=function(){ze.cache++,Hk.tween&&Z()},t.onComplete=function(){Hk.tween=0,a&&a.call(o)},o=Hk.tween=Ne.to(c,t)}var u=L(c,e),f="_scroll"+e.p2;return(c[f]=u).wheelHandler=function(){return Hk.tween&&Hk.tween.kill()&&(Hk.tween=0)},xb(c,"wheel",u.wheelHandler),ne.isTouch&&xb(c,"touchmove",u.wheelHandler),Hk}var Ne,s,Xe,Fe,Ue,We,l,c,Je,Ge,Ve,u,Ke,Qe,f,je,d,p,g,Ze,$e,h,v,b,m,y,R,x,_,w,S,k,et,tt,T,rt,nt,it,ot=1,at=Date.now,D=at(),st=0,lt=0,ct=function _isString(e){return"string"==typeof e},ut=Math.abs,I="right",Y="bottom",ft="width",dt="height",pt="Right",gt="Left",ht="Top",vt="Bottom",bt="padding",mt="margin",yt="Width",H="Height",xt="px",_t=function _getBounds(e,t){var r=t&&"matrix(1, 0, 0, 1, 0, 0)"!==nb(e)[f]&&Ne.to(e,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),n=e.getBoundingClientRect();return r&&r.progress(0).kill(),n},wt={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},St={toggleActions:"play",anticipatePin:0},q={top:0,left:0,center:.5,bottom:1,right:1},X=function _positionMarker(e,t,r,n){var i={display:"block"},o=r[n?"os2":"p2"],a=r[n?"p2":"os2"];e._isFlipped=n,i[r.a+"Percent"]=n?-100:0,i[r.a]=n?"1px":0,i["border"+o+yt]=1,i["border"+a+yt]=0,i[r.p]=t+"px",Ne.set(e,i)},kt=[],Tt={},U={},W=[],V=function _dispatch(e){return U[e]&&U[e].map(function(e){return e()})||W},K=[],Ct=0,Mt=function _refreshAll(e,t){if(Ue=Fe.documentElement,We=Fe.body,l=[Xe,Fe,Ue,We],!st||e||k){Zb(),rt=ne.isRefreshing=!0,ze.forEach(function(e){return Ua(e)&&++e.cacheID&&(e.rec=e())});var r=V("refreshInit");Ze&&ne.sort(),t||Tb(),ze.forEach(function(e){Ua(e)&&(e.smooth&&(e.target.style.scrollBehavior="auto"),e(0))}),kt.slice(0).forEach(function(e){return e.refresh()}),k=!1,kt.forEach(function(e){if(e._subPinOffset&&e.pin){var t=e.vars.horizontal?"offsetWidth":"offsetHeight",r=e.pin[t];e.revert(!0,1),e.adjustPinSpacing(e.pin[t]-r),e.refresh()}}),et=1,$b(!0),kt.forEach(function(e){var t=Ra(e.scroller,e._dir),r="max"===e.vars.end||e._endClamp&&e.end>t,n=e._startClamp&&e.start>=t;(r||n)&&e.setPositions(n?t-1:e.start,r?Math.max(n?t:e.start+1,t):e.end,!0)}),$b(!1),et=0,r.forEach(function(e){return e&&e.render&&e.render(-1)}),ze.forEach(function(e){Ua(e)&&(e.smooth&&requestAnimationFrame(function(){return e.target.style.scrollBehavior="smooth"}),e.rec&&e(e.rec))}),Ub(_,1),c.pause(),Ct++,Z(rt=2),kt.forEach(function(e){return Ua(e.vars.onRefresh)&&e.vars.onRefresh(e)}),rt=ne.isRefreshing=!1,V("refresh")}else xb(ne,"scrollEnd",Pb)},j=0,Et=1,Z=function _updateAll(e){if(2===e||!rt&&!k){ne.isUpdating=!0,it&&it.update(0);var t=kt.length,r=at(),n=50<=r-D,i=t&&kt[0].scroll();if(Et=i<j?-1:1,rt||(j=i),n&&(st&&!Qe&&200<r-st&&(st=0,V("scrollEnd")),Ve=D,D=r),Et<0){for(je=t;0<je--;)kt[je]&&kt[je].update(0,n);Et=1}else for(je=0;je<t;je++)kt[je]&&kt[je].update(0,n);ne.isUpdating=!1}T=0},$=["left","top",Y,I,mt+vt,mt+pt,mt+ht,mt+gt,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],ee=$.concat([ft,dt,"boxSizing","max"+yt,"max"+H,"position",mt,bt,bt+ht,bt+pt,bt+vt,bt+gt]),te=/([A-Z])/g,Pt=function _setState(e){if(e){var t,r,n=e.t.style,i=e.length,o=0;for((e.t._gsap||Ne.core.getCache(e.t)).uncache=1;o<i;o+=2)r=e[o+1],t=e[o],r?n[t]=r:n[t]&&n.removeProperty(t.replace(te,"-$1").toLowerCase())}},Ot={left:0,top:0},re=/(webkit|moz|length|cssText|inset)/i,ne=(ScrollTrigger.prototype.init=function init(P,O){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),lt){var A,n,p,R,D,B,I,Y,H,q,N,e,X,F,U,W,G,V,t,K,b,Q,j,m,Z,y,$,x,r,_,w,ee,i,g,te,re,ne,S,o,k=(P=pb(ct(P)||Va(P)||P.nodeType?{trigger:P}:P,St)).onUpdate,T=P.toggleClass,a=P.id,C=P.onToggle,ie=P.onRefresh,M=P.scrub,oe=P.trigger,ae=P.pin,se=P.pinSpacing,le=P.invalidateOnRefresh,E=P.anticipatePin,s=P.onScrubComplete,h=P.onSnapComplete,ce=P.once,ue=P.snap,fe=P.pinReparent,l=P.pinSpacer,de=P.containerAnimation,pe=P.fastScrollEnd,ge=P.preventOverlaps,he=P.horizontal||P.containerAnimation&&!1!==P.horizontal?He:qe,ve=!M&&0!==M,be=J(P.scroller||Xe),c=Ne.core.getCache(be),me=Ma(be),ye="fixed"===("pinType"in P?P.pinType:z(be,"pinType")||me&&"fixed"),xe=[P.onEnter,P.onLeave,P.onEnterBack,P.onLeaveBack],_e=ve&&P.toggleActions.split(" "),we="markers"in P?P.markers:St.markers,Se=me?0:parseFloat(nb(be)["border"+he.p2+yt])||0,ke=this,Te=P.onRefreshInit&&function(){return P.onRefreshInit(ke)},Ce=function _getSizeFunc(e,t,r){var n=r.d,i=r.d2,o=r.a;return(o=z(e,"getBoundingClientRect"))?function(){return o()[n]}:function(){return(t?Na(i):e["client"+i])||0}}(be,me,he),Me=function _getOffsetsFunc(e,t){return!t||~Ye.indexOf(e)?Oa(e):function(){return Ot}}(be,me),Ee=0,Pe=0,Oe=0,Ae=L(be,he);if(ke._startClamp=ke._endClamp=!1,ke._dir=he,E*=45,ke.scroller=be,ke.scroll=de?de.time.bind(de):Ae,R=Ae(),ke.vars=P,O=O||P.animation,"refreshPriority"in P&&(Ze=1,-9999===P.refreshPriority&&(it=ke)),c.tweenScroll=c.tweenScroll||{top:sc(be,qe),left:sc(be,He)},ke.tweenTo=A=c.tweenScroll[he.p],ke.scrubDuration=function(e){(i=Va(e)&&e)?ee?ee.duration(e):ee=Ne.to(O,{ease:"expo",totalProgress:"+=0",inherit:!1,duration:i,paused:!0,onComplete:function onComplete(){return s&&s(ke)}}):(ee&&ee.progress(1).kill(),ee=0)},O&&(O.vars.lazy=!1,O._initted&&!ke.isReverted||!1!==O.vars.immediateRender&&!1!==P.immediateRender&&O.duration()&&O.render(0,!0,!0),ke.animation=O.pause(),(O.scrollTrigger=ke).scrubDuration(M),_=0,a=a||O.vars.id),ue&&(Wa(ue)&&!ue.push||(ue={snapTo:ue}),"scrollBehavior"in We.style&&Ne.set(me?[We,Ue]:be,{scrollBehavior:"auto"}),ze.forEach(function(e){return Ua(e)&&e.target===(me?Fe.scrollingElement||Ue:be)&&(e.smooth=!1)}),p=Ua(ue.snapTo)?ue.snapTo:"labels"===ue.snapTo?function _getClosestLabel(t){return function(e){return Ne.utils.snap(sb(t),e)}}(O):"labelsDirectional"===ue.snapTo?function _getLabelAtDirection(r){return function(e,t){return ub(sb(r))(e,t.direction)}}(O):!1!==ue.directional?function(e,t){return ub(ue.snapTo)(e,at()-Pe<500?0:t.direction)}:Ne.utils.snap(ue.snapTo),g=ue.duration||{min:.1,max:2},g=Wa(g)?Ge(g.min,g.max):Ge(g,g),te=Ne.delayedCall(ue.delay||i/2||.1,function(){var e=Ae(),t=at()-Pe<500,r=A.tween;if(!(t||Math.abs(ke.getVelocity())<10)||r||Qe||Ee===e)ke.isActive&&Ee!==e&&te.restart(!0);else{var n,i,o=(e-B)/F,a=O&&!ve?O.totalProgress():o,s=t?0:(a-w)/(at()-Ve)*1e3||0,l=Ne.utils.clamp(-o,1-o,ut(s/2)*s/.185),c=o+(!1===ue.inertia?0:l),u=ue.onStart,f=ue.onInterrupt,d=ue.onComplete;if(n=p(c,ke),Va(n)||(n=c),i=Math.max(0,Math.round(B+n*F)),e<=I&&B<=e&&i!==e){if(r&&!r._initted&&r.data<=ut(i-e))return;!1===ue.inertia&&(l=n-o),A(i,{duration:g(ut(.185*Math.max(ut(c-a),ut(n-a))/s/.05||0)),ease:ue.ease||"power3",data:ut(i-e),onInterrupt:function onInterrupt(){return te.restart(!0)&&f&&f(ke)},onComplete:function onComplete(){ke.update(),Ee=Ae(),O&&!ve&&(ee?ee.resetTo("totalProgress",n,O._tTime/O._tDur):O.progress(n)),_=w=O&&!ve?O.totalProgress():ke.progress,h&&h(ke),d&&d(ke)}},e,l*F,i-e-l*F),u&&u(ke,A.tween)}}}).pause()),a&&(Tt[a]=ke),o=(o=(oe=ke.trigger=J(oe||!0!==ae&&ae))&&oe._gsap&&oe._gsap.stRevert)&&o(ke),ae=!0===ae?oe:J(ae),ct(T)&&(T={targets:oe,className:T}),ae&&(!1===se||se===mt||(se=!(!se&&ae.parentNode&&ae.parentNode.style&&"flex"===nb(ae.parentNode).display)&&bt),ke.pin=ae,(n=Ne.core.getCache(ae)).spacer?U=n.pinState:(l&&((l=J(l))&&!l.nodeType&&(l=l.current||l.nativeElement),n.spacerIsNative=!!l,l&&(n.spacerState=kc(l))),n.spacer=V=l||Fe.createElement("div"),V.classList.add("pin-spacer"),a&&V.classList.add("pin-spacer-"+a),n.pinState=U=kc(ae)),!1!==P.force3D&&Ne.set(ae,{force3D:!0}),ke.spacer=V=n.spacer,r=nb(ae),m=r[se+he.os2],K=Ne.getProperty(ae),b=Ne.quickSetter(ae,he.a,xt),hc(ae,V,r),G=kc(ae)),we){e=Wa(we)?pb(we,wt):wt,q=Eb("scroller-start",a,be,he,e,0),N=Eb("scroller-end",a,be,he,e,0,q),t=q["offset"+he.op.d2];var u=J(z(be,"content")||be);Y=this.markerStart=Eb("start",a,u,he,e,t,0,de),H=this.markerEnd=Eb("end",a,u,he,e,t,0,de),de&&(S=Ne.quickSetter([Y,H],he.a,xt)),ye||Ye.length&&!0===z(be,"fixedMarkers")||(function _makePositionable(e){var t=nb(e).position;e.style.position="absolute"===t||"fixed"===t?t:"relative"}(me?We:be),Ne.set([q,N],{force3D:!0}),y=Ne.quickSetter(q,he.a,xt),x=Ne.quickSetter(N,he.a,xt))}if(de){var f=de.vars.onUpdate,d=de.vars.onUpdateParams;de.eventCallback("onUpdate",function(){ke.update(0,0,1),f&&f.apply(de,d||[])})}if(ke.previous=function(){return kt[kt.indexOf(ke)-1]},ke.next=function(){return kt[kt.indexOf(ke)+1]},ke.revert=function(e,t){if(!t)return ke.kill(!0);var r=!1!==e||!ke.enabled,n=Ke;r!==ke.isReverted&&(r&&(re=Math.max(Ae(),ke.scroll.rec||0),Oe=ke.progress,ne=O&&O.progress()),Y&&[Y,H,q,N].forEach(function(e){return e.style.display=r?"none":"block"}),r&&(Ke=ke).update(r),!ae||fe&&ke.isActive||(r?function _swapPinOut(e,t,r){Pt(r);var n=e._gsap;if(n.spacerIsNative)Pt(n.spacerState);else if(e._gsap.swappedIn){var i=t.parentNode;i&&(i.insertBefore(e,t),i.removeChild(t))}e._gsap.swappedIn=!1}(ae,V,U):hc(ae,V,nb(ae),Z)),r||ke.update(r),Ke=n,ke.isReverted=r)},ke.refresh=function(e,t,r,n){if(!Ke&&ke.enabled||t)if(ae&&e&&st)xb(ScrollTrigger,"scrollEnd",Pb);else{!rt&&Te&&Te(ke),Ke=ke,A.tween&&!r&&(A.tween.kill(),A.tween=0),ee&&ee.pause(),le&&O&&(O.revert({kill:!1}).invalidate(),O.getChildren&&O.getChildren(!0,!0,!1).forEach(function(e){return e.vars.immediateRender&&e.render(0,!0,!0)})),ke.isReverted||ke.revert(!0,!0),ke._subPinOffset=!1;var i,o,a,s,l,c,u,f,d,p,g,h,v,b=Ce(),m=Me(),y=de?de.duration():Ra(be,he),x=F<=.01||!F,_=0,w=n||0,S=Wa(r)?r.end:P.end,k=P.endTrigger||oe,T=Wa(r)?r.start:P.start||(0!==P.start&&oe?ae?"0 0":"0 100%":0),C=ke.pinnedContainer=P.pinnedContainer&&J(P.pinnedContainer,ke),M=oe&&Math.max(0,kt.indexOf(ke))||0,E=M;for(we&&Wa(r)&&(h=Ne.getProperty(q,he.p),v=Ne.getProperty(N,he.p));0<E--;)(c=kt[E]).end||c.refresh(0,1)||(Ke=ke),!(u=c.pin)||u!==oe&&u!==ae&&u!==C||c.isReverted||((p=p||[]).unshift(c),c.revert(!0,!0)),c!==kt[E]&&(M--,E--);for(Ua(T)&&(T=T(ke)),T=Da(T,"start",ke),B=nc(T,oe,b,he,Ae(),Y,q,ke,m,Se,ye,y,de,ke._startClamp&&"_startClamp")||(ae?-.001:0),Ua(S)&&(S=S(ke)),ct(S)&&!S.indexOf("+=")&&(~S.indexOf(" ")?S=(ct(T)?T.split(" ")[0]:"")+S:(_=Db(S.substr(2),b),S=ct(T)?T:(de?Ne.utils.mapRange(0,de.duration(),de.scrollTrigger.start,de.scrollTrigger.end,B):B)+_,k=oe)),S=Da(S,"end",ke),I=Math.max(B,nc(S||(k?"100% 0":y),k,b,he,Ae()+_,H,N,ke,m,Se,ye,y,de,ke._endClamp&&"_endClamp"))||-.001,_=0,E=M;E--;)(u=(c=kt[E]).pin)&&c.start-c._pinPush<=B&&!de&&0<c.end&&(i=c.end-(ke._startClamp?Math.max(0,c.start):c.start),(u===oe&&c.start-c._pinPush<B||u===C)&&isNaN(T)&&(_+=i*(1-c.progress)),u===ae&&(w+=i));if(B+=_,I+=_,ke._startClamp&&(ke._startClamp+=_),ke._endClamp&&!rt&&(ke._endClamp=I||-.001,I=Math.min(I,Ra(be,he))),F=I-B||(B-=.01)&&.001,x&&(Oe=Ne.utils.clamp(0,1,Ne.utils.normalize(B,I,re))),ke._pinPush=w,Y&&_&&((i={})[he.a]="+="+_,C&&(i[he.p]="-="+Ae()),Ne.set([Y,H],i)),!ae||et&&ke.end>=Ra(be,he)){if(oe&&Ae()&&!de)for(o=oe.parentNode;o&&o!==We;)o._pinOffset&&(B-=o._pinOffset,I-=o._pinOffset),o=o.parentNode}else i=nb(ae),s=he===qe,a=Ae(),Q=parseFloat(K(he.a))+w,!y&&1<I&&(g={style:g=(me?Fe.scrollingElement||Ue:be).style,value:g["overflow"+he.a.toUpperCase()]},me&&"scroll"!==nb(We)["overflow"+he.a.toUpperCase()]&&(g.style["overflow"+he.a.toUpperCase()]="scroll")),hc(ae,V,i),G=kc(ae),o=_t(ae,!0),f=ye&&L(be,s?He:qe)(),se?((Z=[se+he.os2,F+w+xt]).t=V,(E=se===bt?rb(ae,he)+F+w:0)&&(Z.push(he.d,E+xt),"auto"!==V.style.flexBasis&&(V.style.flexBasis=E+xt)),Pt(Z),C&&kt.forEach(function(e){e.pin===C&&!1!==e.vars.pinSpacing&&(e._subPinOffset=!0)}),ye&&Ae(re)):(E=rb(ae,he))&&"auto"!==V.style.flexBasis&&(V.style.flexBasis=E+xt),ye&&((l={top:o.top+(s?a-B:f)+xt,left:o.left+(s?f:a-B)+xt,boxSizing:"border-box",position:"fixed"})[ft]=l.maxWidth=Math.ceil(o.width)+xt,l[dt]=l.maxHeight=Math.ceil(o.height)+xt,l[mt]=l[mt+ht]=l[mt+pt]=l[mt+vt]=l[mt+gt]="0",l[bt]=i[bt],l[bt+ht]=i[bt+ht],l[bt+pt]=i[bt+pt],l[bt+vt]=i[bt+vt],l[bt+gt]=i[bt+gt],W=function _copyState(e,t,r){for(var n,i=[],o=e.length,a=r?8:0;a<o;a+=2)n=e[a],i.push(n,n in t?t[n]:e[a+1]);return i.t=e.t,i}(U,l,fe),rt&&Ae(0)),O?(d=O._initted,$e(1),O.render(O.duration(),!0,!0),j=K(he.a)-Q+F+w,$=1<Math.abs(F-j),ye&&$&&W.splice(W.length-2,2),O.render(0,!0,!0),d||O.invalidate(!0),O.parent||O.totalTime(O.totalTime()),$e(0)):j=F,g&&(g.value?g.style["overflow"+he.a.toUpperCase()]=g.value:g.style.removeProperty("overflow-"+he.a));p&&p.forEach(function(e){return e.revert(!1,!0)}),ke.start=B,ke.end=I,R=D=rt?re:Ae(),de||rt||(R<re&&Ae(re),ke.scroll.rec=0),ke.revert(!1,!0),Pe=at(),te&&(Ee=-1,te.restart(!0)),Ke=0,O&&ve&&(O._initted||ne)&&O.progress()!==ne&&O.progress(ne||0,!0).render(O.time(),!0,!0),(x||Oe!==ke.progress||de||le||O&&!O._initted)&&(O&&!ve&&(O._initted||Oe||!1!==O.vars.immediateRender)&&O.totalProgress(de&&B<-.001&&!Oe?Ne.utils.normalize(B,I,0):Oe,!0),ke.progress=x||(R-B)/F===Oe?0:Oe),ae&&se&&(V._pinOffset=Math.round(ke.progress*j)),ee&&ee.invalidate(),isNaN(h)||(h-=Ne.getProperty(q,he.p),v-=Ne.getProperty(N,he.p),rc(q,he,h),rc(Y,he,h-(n||0)),rc(N,he,v),rc(H,he,v-(n||0))),x&&!rt&&ke.update(),!ie||rt||X||(X=!0,ie(ke),X=!1)}},ke.getVelocity=function(){return(Ae()-D)/(at()-Ve)*1e3||0},ke.endAnimation=function(){Xa(ke.callbackAnimation),O&&(ee?ee.progress(1):O.paused()?ve||Xa(O,ke.direction<0,1):Xa(O,O.reversed()))},ke.labelToScroll=function(e){return O&&O.labels&&(B||ke.refresh()||B)+O.labels[e]/O.duration()*F||0},ke.getTrailing=function(t){var e=kt.indexOf(ke),r=0<ke.direction?kt.slice(0,e).reverse():kt.slice(e+1);return(ct(t)?r.filter(function(e){return e.vars.preventOverlaps===t}):r).filter(function(e){return 0<ke.direction?e.end<=B:e.start>=I})},ke.update=function(e,t,r){if(!de||r||e){var n,i,o,a,s,l,c,u=!0===rt?re:ke.scroll(),f=e?0:(u-B)/F,d=f<0?0:1<f?1:f||0,p=ke.progress;if(t&&(D=R,R=de?Ae():u,ue&&(w=_,_=O&&!ve?O.totalProgress():d)),E&&ae&&!Ke&&!ot&&st&&(!d&&B<u+(u-D)/(at()-Ve)*E?d=1e-4:1===d&&I>u+(u-D)/(at()-Ve)*E&&(d=.9999)),d!==p&&ke.enabled){if(a=(s=(n=ke.isActive=!!d&&d<1)!=(!!p&&p<1))||!!d!=!!p,ke.direction=p<d?1:-1,ke.progress=d,a&&!Ke&&(i=d&&!p?0:1===d?1:1===p?2:3,ve&&(o=!s&&"none"!==_e[i+1]&&_e[i+1]||_e[i],c=O&&("complete"===o||"reset"===o||o in O))),ge&&(s||c)&&(c||M||!O)&&(Ua(ge)?ge(ke):ke.getTrailing(ge).forEach(function(e){return e.endAnimation()})),ve||(!ee||Ke||ot?O&&O.totalProgress(d,!(!Ke||!Pe&&!e)):(ee._dp._time-ee._start!==ee._time&&ee.render(ee._dp._time-ee._start),ee.resetTo?ee.resetTo("totalProgress",d,O._tTime/O._tDur):(ee.vars.totalProgress=d,ee.invalidate().restart()))),ae)if(e&&se&&(V.style[se+he.os2]=m),ye){if(a){if(l=!e&&p<d&&u<I+1&&u+1>=Ra(be,he),fe)if(e||!n&&!l)pc(ae,V);else{var g=_t(ae,!0),h=u-B;pc(ae,We,g.top+(he===qe?h:0)+xt,g.left+(he===qe?0:h)+xt)}Pt(n||l?W:G),$&&d<1&&n||b(Q+(1!==d||l?0:j))}}else b(Ja(Q+j*d));!ue||A.tween||Ke||ot||te.restart(!0),T&&(s||ce&&d&&(d<1||!tt))&&Je(T.targets).forEach(function(e){return e.classList[n||ce?"add":"remove"](T.className)}),!k||ve||e||k(ke),a&&!Ke?(ve&&(c&&("complete"===o?O.pause().totalProgress(1):"reset"===o?O.restart(!0).pause():"restart"===o?O.restart(!0):O[o]()),k&&k(ke)),!s&&tt||(C&&s&&Ya(ke,C),xe[i]&&Ya(ke,xe[i]),ce&&(1===d?ke.kill(!1,1):xe[i]=0),s||xe[i=1===d?1:3]&&Ya(ke,xe[i])),pe&&!n&&Math.abs(ke.getVelocity())>(Va(pe)?pe:2500)&&(Xa(ke.callbackAnimation),ee?ee.progress(1):Xa(O,"reverse"===o?1:!d,1))):ve&&k&&!Ke&&k(ke)}if(x){var v=de?u/de.duration()*(de._caScrollDist||0):u;y(v+(q._isFlipped?1:0)),x(v)}S&&S(-u/de.duration()*(de._caScrollDist||0))}},ke.enable=function(e,t){ke.enabled||(ke.enabled=!0,xb(be,"resize",Mb),me||xb(be,"scroll",Kb),Te&&xb(ScrollTrigger,"refreshInit",Te),!1!==e&&(ke.progress=Oe=0,R=D=Ee=Ae()),!1!==t&&ke.refresh())},ke.getTween=function(e){return e&&A?A.tween:ee},ke.setPositions=function(e,t,r,n){if(de){var i=de.scrollTrigger,o=de.duration(),a=i.end-i.start;e=i.start+a*e/o,t=i.start+a*t/o}ke.refresh(!1,!1,{start:Ea(e,r&&!!ke._startClamp),end:Ea(t,r&&!!ke._endClamp)},n),ke.update()},ke.adjustPinSpacing=function(e){if(Z&&e){var t=Z.indexOf(he.d)+1;Z[t]=parseFloat(Z[t])+e+xt,Z[1]=parseFloat(Z[1])+e+xt,Pt(Z)}},ke.disable=function(e,t){if(ke.enabled&&(!1!==e&&ke.revert(!0,!0),ke.enabled=ke.isActive=!1,t||ee&&ee.pause(),re=0,n&&(n.uncache=1),Te&&yb(ScrollTrigger,"refreshInit",Te),te&&(te.pause(),A.tween&&A.tween.kill()&&(A.tween=0)),!me)){for(var r=kt.length;r--;)if(kt[r].scroller===be&&kt[r]!==ke)return;yb(be,"resize",Mb),me||yb(be,"scroll",Kb)}},ke.kill=function(e,t){ke.disable(e,t),ee&&!t&&ee.kill(),a&&delete Tt[a];var r=kt.indexOf(ke);0<=r&&kt.splice(r,1),r===je&&0<Et&&je--,r=0,kt.forEach(function(e){return e.scroller===ke.scroller&&(r=1)}),r||rt||(ke.scroll.rec=0),O&&(O.scrollTrigger=null,e&&O.revert({kill:!1}),t||O.kill()),Y&&[Y,H,q,N].forEach(function(e){return e.parentNode&&e.parentNode.removeChild(e)}),it===ke&&(it=0),ae&&(n&&(n.uncache=1),r=0,kt.forEach(function(e){return e.pin===ae&&r++}),r||(n.spacer=0)),P.onKill&&P.onKill(ke)},kt.push(ke),ke.enable(!1,!1),o&&o(ke),O&&O.add&&!F){var v=ke.update;ke.update=function(){ke.update=v,ze.cache++,B||I||ke.refresh()},Ne.delayedCall(.01,ke.update),F=.01,B=I=0}else ke.refresh();ae&&function _queueRefreshAll(){if(nt!==Ct){var e=nt=Ct;requestAnimationFrame(function(){return e===Ct&&Mt(!0)})}}()}else this.update=this.refresh=this.kill=Ia},ScrollTrigger.register=function register(e){return s||(Ne=e||La(),Ka()&&window.document&&ScrollTrigger.enable(),s=lt),s},ScrollTrigger.defaults=function defaults(e){if(e)for(var t in e)St[t]=e[t];return St},ScrollTrigger.disable=function disable(t,r){lt=0,kt.forEach(function(e){return e[r?"kill":"disable"](t)}),yb(Xe,"wheel",Kb),yb(Fe,"scroll",Kb),clearInterval(u),yb(Fe,"touchcancel",Ia),yb(We,"touchstart",Ia),wb(yb,Fe,"pointerdown,touchstart,mousedown",Ga),wb(yb,Fe,"pointerup,touchend,mouseup",Ha),c.kill(),Sa(yb);for(var e=0;e<ze.length;e+=3)zb(yb,ze[e],ze[e+1]),zb(yb,ze[e],ze[e+2])},ScrollTrigger.enable=function enable(){if(Xe=window,Fe=document,Ue=Fe.documentElement,We=Fe.body,Ne&&(Je=Ne.utils.toArray,Ge=Ne.utils.clamp,x=Ne.core.context||Ia,$e=Ne.core.suppressOverwrites||Ia,_=Xe.history.scrollRestoration||"auto",j=Xe.pageYOffset||0,Ne.core.globals("ScrollTrigger",ScrollTrigger),We)){lt=1,(w=document.createElement("div")).style.height="100vh",w.style.position="absolute",Zb(),function _rafBugFix(){return lt&&requestAnimationFrame(_rafBugFix)}(),E.register(Ne),ScrollTrigger.isTouch=E.isTouch,R=E.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),b=1===E.isTouch,xb(Xe,"wheel",Kb),l=[Xe,Fe,Ue,We],Ne.matchMedia?(ScrollTrigger.matchMedia=function(e){var t,r=Ne.matchMedia();for(t in e)r.add(t,e[t]);return r},Ne.addEventListener("matchMediaInit",function(){return Tb()}),Ne.addEventListener("matchMediaRevert",function(){return Sb()}),Ne.addEventListener("matchMedia",function(){Mt(0,1),V("matchMedia")}),Ne.matchMedia().add("(orientation: portrait)",function(){return Lb(),Lb})):console.warn("Requires GSAP 3.11.0 or later"),Lb(),xb(Fe,"scroll",Kb);var e,t,r=We.hasAttribute("style"),n=We.style,i=n.borderTopStyle,o=Ne.core.Animation.prototype;for(o.revert||Object.defineProperty(o,"revert",{value:function value(){return this.time(-.01,!0)}}),n.borderTopStyle="solid",e=_t(We),qe.m=Math.round(e.top+qe.sc())||0,He.m=Math.round(e.left+He.sc())||0,i?n.borderTopStyle=i:n.removeProperty("border-top-style"),r||(We.setAttribute("style",""),We.removeAttribute("style")),u=setInterval(Jb,250),Ne.delayedCall(.5,function(){return ot=0}),xb(Fe,"touchcancel",Ia),xb(We,"touchstart",Ia),wb(xb,Fe,"pointerdown,touchstart,mousedown",Ga),wb(xb,Fe,"pointerup,touchend,mouseup",Ha),f=Ne.utils.checkPrefix("transform"),ee.push(f),s=at(),c=Ne.delayedCall(.2,Mt).pause(),g=[Fe,"visibilitychange",function(){var e=Xe.innerWidth,t=Xe.innerHeight;Fe.hidden?(d=e,p=t):d===e&&p===t||Mb()},Fe,"DOMContentLoaded",Mt,Xe,"load",Mt,Xe,"resize",Mb],Sa(xb),kt.forEach(function(e){return e.enable(0,1)}),t=0;t<ze.length;t+=3)zb(yb,ze[t],ze[t+1]),zb(yb,ze[t],ze[t+2])}},ScrollTrigger.config=function config(e){"limitCallbacks"in e&&(tt=!!e.limitCallbacks);var t=e.syncInterval;t&&clearInterval(u)||(u=t)&&setInterval(Jb,t),"ignoreMobileResize"in e&&(b=1===ScrollTrigger.isTouch&&e.ignoreMobileResize),"autoRefreshEvents"in e&&(Sa(yb)||Sa(xb,e.autoRefreshEvents||"none"),h=-1===(e.autoRefreshEvents+"").indexOf("resize"))},ScrollTrigger.scrollerProxy=function scrollerProxy(e,t){var r=J(e),n=ze.indexOf(r),i=Ma(r);~n&&ze.splice(n,i?6:2),t&&(i?Ye.unshift(Xe,t,We,t,Ue,t):Ye.unshift(r,t))},ScrollTrigger.clearMatchMedia=function clearMatchMedia(t){kt.forEach(function(e){return e._ctx&&e._ctx.query===t&&e._ctx.kill(!0,!0)})},ScrollTrigger.isInViewport=function isInViewport(e,t,r){var n=(ct(e)?J(e):e).getBoundingClientRect(),i=n[r?ft:dt]*t||0;return r?0<n.right-i&&n.left+i<Xe.innerWidth:0<n.bottom-i&&n.top+i<Xe.innerHeight},ScrollTrigger.positionInViewport=function positionInViewport(e,t,r){ct(e)&&(e=J(e));var n=e.getBoundingClientRect(),i=n[r?ft:dt],o=null==t?i/2:t in q?q[t]*i:~t.indexOf("%")?parseFloat(t)*i/100:parseFloat(t)||0;return r?(n.left+o)/Xe.innerWidth:(n.top+o)/Xe.innerHeight},ScrollTrigger.killAll=function killAll(e){if(kt.slice(0).forEach(function(e){return"ScrollSmoother"!==e.vars.id&&e.kill()}),!0!==e){var t=U.killAll||[];U={},t.forEach(function(e){return e()})}},ScrollTrigger);function ScrollTrigger(e,t){s||ScrollTrigger.register(Ne)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),x(this),this.init(e,t)}ne.version="3.13.0",ne.saveStyles=function(e){return e?Je(e).forEach(function(e){if(e&&e.style){var t=K.indexOf(e);0<=t&&K.splice(t,5),K.push(e,e.style.cssText,e.getBBox&&e.getAttribute("transform"),Ne.core.getCache(e),x())}}):K},ne.revert=function(e,t){return Tb(!e,t)},ne.create=function(e,t){return new ne(e,t)},ne.refresh=function(e){return e?Mb(!0):(s||ne.register())&&Mt(!0)},ne.update=function(e){return++ze.cache&&Z(!0===e?2:0)},ne.clearScrollMemory=Ub,ne.maxScroll=function(e,t){return Ra(e,t?He:qe)},ne.getScrollFunc=function(e,t){return L(J(e),t?He:qe)},ne.getById=function(e){return Tt[e]},ne.getAll=function(){return kt.filter(function(e){return"ScrollSmoother"!==e.vars.id})},ne.isScrolling=function(){return!!st},ne.snapDirectional=ub,ne.addEventListener=function(e,t){var r=U[e]||(U[e]=[]);~r.indexOf(t)||r.push(t)},ne.removeEventListener=function(e,t){var r=U[e],n=r&&r.indexOf(t);0<=n&&r.splice(n,1)},ne.batch=function(e,t){function Jp(e,t){var r=[],n=[],i=Ne.delayedCall(o,function(){t(r,n),r=[],n=[]}).pause();return function(e){r.length||i.restart(!0),r.push(e.trigger),n.push(e),a<=r.length&&i.progress(1)}}var r,n=[],i={},o=t.interval||.016,a=t.batchMax||1e9;for(r in t)i[r]="on"===r.substr(0,2)&&Ua(t[r])&&"onRefreshInit"!==r?Jp(0,t[r]):t[r];return Ua(a)&&(a=a(),xb(ne,"refresh",function(){return a=t.batchMax()})),Je(e).forEach(function(e){var t={};for(r in i)t[r]=i[r];t.trigger=e,n.push(ne.create(t))}),n};function uc(e,t,r,n){return n<t?e(n):t<0&&e(0),n<r?(n-t)/(r-t):r<0?t/(t-r):1}function vc(e,t){!0===t?e.style.removeProperty("touch-action"):e.style.touchAction=!0===t?"auto":t?"pan-"+t+(E.isTouch?" pinch-zoom":""):"none",e===Ue&&vc(We,t)}function xc(e){var t,r=e.event,n=e.target,i=e.axis,o=(r.changedTouches?r.changedTouches[0]:r).target,a=o._gsap||Ne.core.getCache(o),s=at();if(!a._isScrollT||2e3<s-a._isScrollT){for(;o&&o!==We&&(o.scrollHeight<=o.clientHeight&&o.scrollWidth<=o.clientWidth||!oe[(t=nb(o)).overflowY]&&!oe[t.overflowX]);)o=o.parentNode;a._isScroll=o&&o!==n&&!Ma(o)&&(oe[(t=nb(o)).overflowY]||oe[t.overflowX]),a._isScrollT=s}!a._isScroll&&"x"!==i||(r.stopPropagation(),r._gsapAllow=!0)}function yc(e,t,r,n){return E.create({target:e,capture:!0,debounce:!1,lockAxis:!0,type:t,onWheel:n=n&&xc,onPress:n,onDrag:n,onScroll:n,onEnable:function onEnable(){return r&&xb(Fe,E.eventTypes[0],se,!1,!0)},onDisable:function onDisable(){return yb(Fe,E.eventTypes[0],se,!0)}})}function Cc(e){function Gq(){return i=!1}function Jq(){o=Ra(p,qe),T=Ge(R?1:0,o),f&&(k=Ge(0,Ra(p,He))),l=Ct}function Kq(){v._gsap.y=Ja(parseFloat(v._gsap.y)+b.offset)+"px",v.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(v._gsap.y)+", 0, 1)",b.offset=b.cacheID=0}function Qq(){Jq(),a.isActive()&&a.vars.scrollY>o&&(b()>o?a.progress(1)&&b(o):a.resetTo("scrollY",o))}Wa(e)||(e={}),e.preventDefault=e.isNormalizer=e.allowClicks=!0,e.type||(e.type="wheel,touch"),e.debounce=!!e.debounce,e.id=e.id||"normalizer";var n,o,l,i,a,c,u,s,f=e.normalizeScrollX,t=e.momentum,r=e.allowNestedScroll,d=e.onRelease,p=J(e.target)||Ue,g=Ne.core.globals().ScrollSmoother,h=g&&g.get(),v=R&&(e.content&&J(e.content)||h&&!1!==e.content&&!h.smooth()&&h.content()),b=L(p,qe),m=L(p,He),y=1,x=(E.isTouch&&Xe.visualViewport?Xe.visualViewport.scale*Xe.visualViewport.width:Xe.outerWidth)/Xe.innerWidth,_=0,w=Ua(t)?function(){return t(n)}:function(){return t||2.8},S=yc(p,e.type,!0,r),k=Ia,T=Ia;return v&&Ne.set(v,{y:"+=0"}),e.ignoreCheck=function(e){return R&&"touchmove"===e.type&&function ignoreDrag(){if(i){requestAnimationFrame(Gq);var e=Ja(n.deltaY/2),t=T(b.v-e);if(v&&t!==b.v+b.offset){b.offset=t-b.v;var r=Ja((parseFloat(v&&v._gsap.y)||0)-b.offset);v.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+r+", 0, 1)",v._gsap.y=r+"px",b.cacheID=ze.cache,Z()}return!0}b.offset&&Kq(),i=!0}()||1.05<y&&"touchstart"!==e.type||n.isGesturing||e.touches&&1<e.touches.length},e.onPress=function(){i=!1;var e=y;y=Ja((Xe.visualViewport&&Xe.visualViewport.scale||1)/x),a.pause(),e!==y&&vc(p,1.01<y||!f&&"x"),c=m(),u=b(),Jq(),l=Ct},e.onRelease=e.onGestureStart=function(e,t){if(b.offset&&Kq(),t){ze.cache++;var r,n,i=w();f&&(n=(r=m())+.05*i*-e.velocityX/.227,i*=uc(m,r,n,Ra(p,He)),a.vars.scrollX=k(n)),n=(r=b())+.05*i*-e.velocityY/.227,i*=uc(b,r,n,Ra(p,qe)),a.vars.scrollY=T(n),a.invalidate().duration(i).play(.01),(R&&a.vars.scrollY>=o||o-1<=r)&&Ne.to({},{onUpdate:Qq,duration:i})}else s.restart(!0);d&&d(e)},e.onWheel=function(){a._ts&&a.pause(),1e3<at()-_&&(l=0,_=at())},e.onChange=function(e,t,r,n,i){if(Ct!==l&&Jq(),t&&f&&m(k(n[2]===t?c+(e.startX-e.x):m()+t-n[1])),r){b.offset&&Kq();var o=i[2]===r,a=o?u+e.startY-e.y:b()+r-i[1],s=T(a);o&&a!==s&&(u+=s-a),b(s)}(r||t)&&Z()},e.onEnable=function(){vc(p,!f&&"x"),ne.addEventListener("refresh",Qq),xb(Xe,"resize",Qq),b.smooth&&(b.target.style.scrollBehavior="auto",b.smooth=m.smooth=!1),S.enable()},e.onDisable=function(){vc(p,!0),yb(Xe,"resize",Qq),ne.removeEventListener("refresh",Qq),S.kill()},e.lockAxis=!1!==e.lockAxis,((n=new E(e)).iOS=R)&&!b()&&b(1),R&&Ne.ticker.add(Ia),s=n._dc,a=Ne.to(n,{ease:"power4",paused:!0,inherit:!1,scrollX:f?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:qc(b,b(),function(){return a.pause()})},onUpdate:Z,onComplete:s.vars.onComplete}),n}var ie,oe={auto:1,scroll:1},ae=/(input|label|select|textarea)/i,se=function _captureInputs(e){var t=ae.test(e.target.tagName);(t||ie)&&(e._gsapAllow=!0,ie=t)};ne.sort=function(e){if(Ua(e))return kt.sort(e);var t=Xe.pageYOffset||0;return ne.getAll().forEach(function(e){return e._sortY=e.trigger?t+e.trigger.getBoundingClientRect().top:e.start+Xe.innerHeight}),kt.sort(e||function(e,t){return-1e6*(e.vars.refreshPriority||0)+(e.vars.containerAnimation?1e6:e._sortY)-((t.vars.containerAnimation?1e6:t._sortY)+-1e6*(t.vars.refreshPriority||0))})},ne.observe=function(e){return new E(e)},ne.normalizeScroll=function(e){if(void 0===e)return v;if(!0===e&&v)return v.enable();if(!1===e)return v&&v.kill(),void(v=e);var t=e instanceof E?e:Cc(e);return v&&v.target===t.target&&v.kill(),Ma(t.target)&&(v=t),t},ne.core={_getVelocityProp:M,_inputObserver:yc,_scrollers:ze,_proxies:Ye,bridge:{ss:function ss(){st||V("scrollStart"),st=at()},ref:function ref(){return Ke}}},La()&&Ne.registerPlugin(ne),e.ScrollTrigger=ne,e.default=ne;if (typeof(window)==="undefined"||window!==e){Object.defineProperty(e,"__esModule",{value:!0})} else {delete e.default}});

