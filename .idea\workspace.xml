<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9e76bb5e-4f2a-4ebe-9888-8b61b890be2e" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.gitattributes" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/.gitignore" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/LawVrikshBetaPage.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/modules.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/modules.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/vcs.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/DEPLOYMENT.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/DEPLOYMENT_FIXES.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/EXCEL_FEATURE.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/Feedback Form.markdown" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/RENDER_EXCEL_ACCESS.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/eslint.config.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/index.html" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/joiningbetalawvrikshbackend/app/database.py" beforeDir="false" afterPath="$PROJECT_DIR$/joiningbetalawvrikshbackend/app/database.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/joiningbetalawvrikshbackend/app/middleware/cors.py" beforeDir="false" afterPath="$PROJECT_DIR$/joiningbetalawvrikshbackend/app/middleware/cors.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/joiningbetalawvrikshbackend/app/repository/creators.py" beforeDir="false" afterPath="$PROJECT_DIR$/joiningbetalawvrikshbackend/app/repository/creators.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/joiningbetalawvrikshbackend/app/repository/feedbacks.py" beforeDir="false" afterPath="$PROJECT_DIR$/joiningbetalawvrikshbackend/app/repository/feedbacks.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/joiningbetalawvrikshbackend/app/repository/not_interested.py" beforeDir="false" afterPath="$PROJECT_DIR$/joiningbetalawvrikshbackend/app/repository/not_interested.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/joiningbetalawvrikshbackend/app/repository/users.py" beforeDir="false" afterPath="$PROJECT_DIR$/joiningbetalawvrikshbackend/app/repository/users.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/joiningbetalawvrikshbackend/app/routes/admin.py" beforeDir="false" afterPath="$PROJECT_DIR$/joiningbetalawvrikshbackend/app/routes/admin.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/joiningbetalawvrikshbackend/wsgi.py" beforeDir="false" afterPath="$PROJECT_DIR$/joiningbetalawvrikshbackend/wsgi.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package-lock.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/public/LVbetaBg.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/public/favicon.ico" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/public/gold.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/public/gold2.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/public/hero.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/public/logo.png" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/public/vite.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/render.yaml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/App.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/App.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/assets/react.svg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/Header.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/Header.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/HomePage.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/HomePage.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/index.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main.tsx" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/vite-env.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tsconfig.app.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tsconfig.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tsconfig.node.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/tsconfig.prod.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/vite.config.ts" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2zj177Ld22HlE69dgY3j9MmhGzO" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Python.wsgi.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/DeepWebResearcher/BetaLawvriksh-Frontend&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\DeepWebResearcher\BetaLawvriksh-Frontend\Frontend\public" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="wsgi" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="LawVrikshBetaPage" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/joiningbetalawvrikshbackend" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/joiningbetalawvrikshbackend/wsgi.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.wsgi" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="9e76bb5e-4f2a-4ebe-9888-8b61b890be2e" name="Changes" comment="" />
      <created>1752229497469</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752229497469</updated>
    </task>
    <servers />
  </component>
</project>