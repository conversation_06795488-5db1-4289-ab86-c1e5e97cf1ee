{"version": 3, "file": "EaselPlugin.min.js", "sources": ["../src/EaselPlugin.js"], "sourcesContent": ["/*!\n * EaselPlugin 3.13.0\n * https://gsap.com\n *\n * @license Copyright 2008-2025, GreenSock. All rights reserved.\n * Subject to the terms at https://gsap.com/standard-license\n * @author: <PERSON>, <EMAIL>\n*/\n/* eslint-disable */\n\nlet gsap, _coreInitted, _win, _createJS, _ColorFilter, _ColorMatrixFilter,\n\t_colorProps = \"redMultiplier,greenMultiplier,blueMultiplier,alphaMultiplier,redOffset,greenOffset,blueOffset,alphaOffset\".split(\",\"),\n\t_windowExists = () => typeof(window) !== \"undefined\",\n\t_getGSAP = () => gsap || (_windowExists() && (gsap = window.gsap) && gsap.registerPlugin && gsap),\n\t_getCreateJS = () => _createJS || (_win && _win.createjs) || _win || {},\n\t_warn = message => console.warn(message),\n\t_cache = target => {\n\t\tlet b = target.getBounds && target.getBounds();\n\t\tif (!b) {\n\t\t\tb = target.nominalBounds || {x:0, y:0, width: 100, height: 100};\n\t\t\ttarget.setBounds && target.setBounds(b.x, b.y, b.width, b.height);\n\t\t}\n\t\ttarget.cache && target.cache(b.x, b.y, b.width, b.height);\n\t\t_warn(\"EaselPlugin: for filters to display in EaselJS, you must call the object's cache() method first. GSAP attempted to use the target's getBounds() for the cache but that may not be completely accurate. \" + target);\n\t},\n\t_parseColorFilter = (target, v, plugin) => {\n\t\tif (!_ColorFilter) {\n\t\t\t_ColorFilter = _getCreateJS().ColorFilter;\n\t\t\tif (!_ColorFilter) {\n\t\t\t\t_warn(\"EaselPlugin error: The EaselJS ColorFilter JavaScript file wasn't loaded.\");\n\t\t\t}\n\t\t}\n\t\tlet filters = target.filters || [],\n\t\t\ti = filters.length,\n\t\t\tc, s, e, a, p, pt;\n\t\twhile (i--) {\n\t\t\tif (filters[i] instanceof _ColorFilter) {\n\t\t\t\ts = filters[i];\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tif (!s) {\n\t\t\ts = new _ColorFilter();\n\t\t\tfilters.push(s);\n\t\t\ttarget.filters = filters;\n\t\t}\n\t\te = s.clone();\n\t\tif (v.tint != null) {\n\t\t\tc = gsap.utils.splitColor(v.tint);\n\t\t\ta = (v.tintAmount != null) ? +v.tintAmount : 1;\n\t\t\te.redOffset = +c[0] * a;\n\t\t\te.greenOffset = +c[1] * a;\n\t\t\te.blueOffset = +c[2] * a;\n\t\t\te.redMultiplier = e.greenMultiplier = e.blueMultiplier = 1 - a;\n\t\t} else {\n\t\t\tfor (p in v) {\n\t\t\t\tif (p !== \"exposure\") if (p !== \"brightness\") {\n\t\t\t\t\te[p] = +v[p];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tif (v.exposure != null) {\n\t\t\te.redOffset = e.greenOffset = e.blueOffset = 255 * (+v.exposure - 1);\n\t\t\te.redMultiplier = e.greenMultiplier = e.blueMultiplier = 1;\n\t\t} else if (v.brightness != null) {\n\t\t\ta = +v.brightness - 1;\n\t\t\te.redOffset = e.greenOffset = e.blueOffset = (a > 0) ? a * 255 : 0;\n\t\t\te.redMultiplier = e.greenMultiplier = e.blueMultiplier = 1 - Math.abs(a);\n\t\t}\n\t\ti = 8;\n\t\twhile (i--) {\n\t\t\tp = _colorProps[i];\n\t\t\tif (s[p] !== e[p]) {\n\t\t\t\tpt = plugin.add(s, p, s[p], e[p], 0, 0, 0, 0, 0, 1);\n\t\t\t\tif (pt) {\n\t\t\t\t\tpt.op = \"easel_colorFilter\";\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tplugin._props.push(\"easel_colorFilter\");\n\t\tif (!target.cacheID) {\n\t\t\t_cache(target);\n\t\t}\n\t},\n\n\t_idMatrix = [1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0],\n\t_lumR = 0.212671,\n\t_lumG = 0.715160,\n\t_lumB = 0.072169,\n\n\t_applyMatrix = (m, m2) => {\n\t\tif (!(m instanceof Array) || !(m2 instanceof Array)) {\n\t\t\treturn m2;\n\t\t}\n\t\tlet temp = [],\n\t\t\ti = 0,\n\t\t\tz = 0,\n\t\t\ty, x;\n\t\tfor (y = 0; y < 4; y++) {\n\t\t\tfor (x = 0; x < 5; x++) {\n\t\t\t\tz = (x === 4) ? m[i + 4] : 0;\n\t\t\t\ttemp[i + x] = m[i]   * m2[x] + m[i+1] * m2[x + 5] +\tm[i+2] * m2[x + 10] + m[i+3] * m2[x + 15] +\tz;\n\t\t\t}\n\t\t\ti += 5;\n\t\t}\n\t\treturn temp;\n\t},\n\n\t_setSaturation = (m, n) => {\n\t\tif (isNaN(n)) {\n\t\t\treturn m;\n\t\t}\n\t\tlet inv = 1 - n,\n\t\t\tr = inv * _lumR,\n\t\t\tg = inv * _lumG,\n\t\t\tb = inv * _lumB;\n\t\treturn _applyMatrix([r + n, g, b, 0, 0, r, g + n, b, 0, 0, r, g, b + n, 0, 0, 0, 0, 0, 1, 0], m);\n\t},\n\n\t_colorize = (m, color, amount) => {\n\t\tif (isNaN(amount)) {\n\t\t\tamount = 1;\n\t\t}\n\t\tlet c = gsap.utils.splitColor(color),\n\t\t\tr = c[0] / 255,\n\t\t\tg = c[1] / 255,\n\t\t\tb = c[2] / 255,\n\t\t\tinv = 1 - amount;\n\t\treturn _applyMatrix([inv + amount * r * _lumR, amount * r * _lumG, amount * r * _lumB, 0, 0, amount * g * _lumR, inv + amount * g * _lumG, amount * g * _lumB, 0, 0, amount * b * _lumR, amount * b * _lumG, inv + amount * b * _lumB, 0, 0, 0, 0, 0, 1, 0], m);\n\t},\n\n\t_setHue = (m, n) => {\n\t\tif (isNaN(n)) {\n\t\t\treturn m;\n\t\t}\n\t\tn *= Math.PI / 180;\n\t\tlet c = Math.cos(n),\n\t\t\ts = Math.sin(n);\n\t\treturn _applyMatrix([(_lumR + (c * (1 - _lumR))) + (s * (-_lumR)), (_lumG + (c * (-_lumG))) + (s * (-_lumG)), (_lumB + (c * (-_lumB))) + (s * (1 - _lumB)), 0, 0, (_lumR + (c * (-_lumR))) + (s * 0.143), (_lumG + (c * (1 - _lumG))) + (s * 0.14), (_lumB + (c * (-_lumB))) + (s * -0.283), 0, 0, (_lumR + (c * (-_lumR))) + (s * (-(1 - _lumR))), (_lumG + (c * (-_lumG))) + (s * _lumG), (_lumB + (c * (1 - _lumB))) + (s * _lumB), 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 1], m);\n\t},\n\n\t_setContrast = (m, n) => {\n\t\tif (isNaN(n)) {\n\t\t\treturn m;\n\t\t}\n\t\tn += 0.01;\n\t\treturn _applyMatrix([n,0,0,0,128 * (1 - n), 0,n,0,0,128 * (1 - n), 0,0,n,0,128 * (1 - n), 0,0,0,1,0], m);\n\t},\n\n\t_parseColorMatrixFilter = (target, v, plugin) => {\n\t\tif (!_ColorMatrixFilter) {\n\t\t\t_ColorMatrixFilter = _getCreateJS().ColorMatrixFilter;\n\t\t\tif (!_ColorMatrixFilter) {\n\t\t\t\t_warn(\"EaselPlugin: The EaselJS ColorMatrixFilter JavaScript file wasn't loaded.\");\n\t\t\t}\n\t\t}\n\t\tlet filters = target.filters || [],\n\t\t\ti = filters.length,\n\t\t\tmatrix, startMatrix, s, pg;\n\t\twhile (--i > -1) {\n\t\t\tif (filters[i] instanceof _ColorMatrixFilter) {\n\t\t\t\ts = filters[i];\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\tif (!s) {\n\t\t\ts = new _ColorMatrixFilter(_idMatrix.slice());\n\t\t\tfilters.push(s);\n\t\t\ttarget.filters = filters;\n\t\t}\n\t\tstartMatrix = s.matrix;\n\t\tmatrix = _idMatrix.slice();\n\t\tif (v.colorize != null) {\n\t\t\tmatrix = _colorize(matrix, v.colorize, Number(v.colorizeAmount));\n\t\t}\n\t\tif (v.contrast != null) {\n\t\t\tmatrix = _setContrast(matrix, Number(v.contrast));\n\t\t}\n\t\tif (v.hue != null) {\n\t\t\tmatrix = _setHue(matrix, Number(v.hue));\n\t\t}\n\t\tif (v.saturation != null) {\n\t\t\tmatrix = _setSaturation(matrix, Number(v.saturation));\n\t\t}\n\n\t\ti = matrix.length;\n\t\twhile (--i > -1) {\n\t\t\tif (matrix[i] !== startMatrix[i]) {\n\t\t\t\tpg = plugin.add(startMatrix, i, startMatrix[i], matrix[i], 0, 0, 0, 0, 0, 1);\n\t\t\t\tif (pg) {\n\t\t\t\t\tpg.op = \"easel_colorMatrixFilter\";\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tplugin._props.push(\"easel_colorMatrixFilter\");\n\t\tif (!target.cacheID) {\n\t\t\t_cache();\n\t\t}\n\n\t\tplugin._matrix = startMatrix;\n\t},\n\n\t_initCore = core => {\n\t\tgsap = core || _getGSAP();\n\t\tif (_windowExists()) {\n\t\t\t_win = window;\n\t\t}\n\t\tif (gsap) {\n\n\t\t\t_coreInitted = 1;\n\t\t}\n\t};\n\n\nexport const EaselPlugin = {\n\tversion: \"3.13.0\",\n\tname: \"easel\",\n\tinit(target, value, tween, index, targets) {\n\t\tif (!_coreInitted) {\n\t\t\t_initCore();\n\t\t\tif (!gsap) {\n\t\t\t\t_warn(\"Please gsap.registerPlugin(EaselPlugin)\");\n\t\t\t}\n\t\t}\n\t\tthis.target = target;\n\t\tlet p, pt, tint, colorMatrix, end, labels, i;\n\t\tfor (p in value) {\n\n\t\t\tend = value[p];\n\t\t\tif (p === \"colorFilter\" || p === \"tint\" || p === \"tintAmount\" || p === \"exposure\" || p === \"brightness\") {\n\t\t\t\tif (!tint) {\n\t\t\t\t\t_parseColorFilter(target, value.colorFilter || value, this);\n\t\t\t\t\ttint = true;\n\t\t\t\t}\n\n\t\t\t} else if (p === \"saturation\" || p === \"contrast\" || p === \"hue\" || p === \"colorize\" || p === \"colorizeAmount\") {\n\t\t\t\tif (!colorMatrix) {\n\t\t\t\t\t_parseColorMatrixFilter(target, value.colorMatrixFilter || value, this);\n\t\t\t\t\tcolorMatrix = true;\n\t\t\t\t}\n\n\t\t\t} else if (p === \"frame\") {\n\t\t\t\tif (typeof(end) === \"string\" && end.charAt(1) !== \"=\" && (labels = target.labels)) {\n\t\t\t\t\tfor (i = 0; i < labels.length; i++) {\n\t\t\t\t\t\tif (labels[i].label === end) {\n\t\t\t\t\t\t\tend = labels[i].position;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tpt = this.add(target, \"gotoAndStop\", target.currentFrame, end, index, targets, Math.round, 0, 0, 1);\n\t\t\t\tif (pt) {\n\t\t\t\t\tpt.op = p;\n\t\t\t\t}\n\n\t\t\t} else if (target[p] != null) {\n\t\t\t\tthis.add(target, p, \"get\", end);\n\t\t\t}\n\n\t\t}\n\t},\n\trender(ratio, data) {\n\t\tlet pt = data._pt;\n\t\twhile (pt) {\n\t\t\tpt.r(ratio, pt.d);\n\t\t\tpt = pt._next;\n\t\t}\n\t\tif (data.target.cacheID) {\n\t\t\tdata.target.updateCache();\n\t\t}\n\t},\n\tregister: _initCore\n};\n\nEaselPlugin.registerCreateJS = createjs => {\n\t_createJS = createjs;\n};\n\n_getGSAP() && gsap.registerPlugin(EaselPlugin);\n\nexport { EaselPlugin as default };"], "names": ["_windowExists", "window", "_getGSAP", "gsap", "registerPlugin", "_getCreateJS", "_createJS", "_win", "createjs", "_warn", "message", "console", "warn", "_cache", "target", "b", "getBounds", "nominalBounds", "x", "y", "width", "height", "setBounds", "cache", "_parseColorFilter", "v", "plugin", "_ColorFilter", "ColorFilter", "c", "s", "e", "a", "p", "pt", "filters", "i", "length", "push", "clone", "tint", "utils", "splitColor", "tintAmount", "redOffset", "greenOffset", "blueOffset", "redMultiplier", "greenMultiplier", "blueMultiplier", "exposure", "brightness", "Math", "abs", "_colorProps", "add", "op", "_props", "cacheID", "_applyMatrix", "m", "m2", "Array", "temp", "z", "_parseColorMatrixFilter", "_ColorMatrixFilter", "ColorMatrixFilter", "matrix", "startMatrix", "pg", "_idMatrix", "slice", "colorize", "_colorize", "color", "amount", "isNaN", "r", "g", "inv", "_lumR", "_lumG", "_lumB", "Number", "colorizeAmount", "contrast", "_setContrast", "n", "hue", "_setHue", "PI", "cos", "sin", "saturation", "_setSaturation", "_matrix", "_initCore", "core", "_coreInitted", "split", "EaselPlugin", "version", "name", "init", "value", "tween", "index", "targets", "colorMatrix", "end", "labels", "colorFilter", "this", "colorMatrixFilter", "char<PERSON>t", "label", "position", "currentFrame", "round", "render", "ratio", "data", "_pt", "d", "_next", "updateCache", "register"], "mappings": ";;;;;;;;;6MAYiB,SAAhBA,UAAyC,oBAAZC,OAClB,SAAXC,WAAiBC,GAASH,MAAoBG,EAAOF,OAAOE,OAASA,EAAKC,gBAAkBD,EAC7E,SAAfE,WAAqBC,GAAcC,GAAQA,EAAKC,UAAaD,GAAQ,GAC7D,SAARE,EAAQC,UAAWC,QAAQC,KAAKF,GACvB,SAATG,EAASC,OACJC,EAAID,EAAOE,WAAaF,EAAOE,YAC9BD,IACJA,EAAID,EAAOG,eAAiB,CAACC,EAAE,EAAGC,EAAE,EAAGC,MAAO,IAAKC,OAAQ,KAC3DP,EAAOQ,WAAaR,EAAOQ,UAAUP,EAAEG,EAAGH,EAAEI,EAAGJ,EAAEK,MAAOL,EAAEM,SAE3DP,EAAOS,OAAST,EAAOS,MAAMR,EAAEG,EAAGH,EAAEI,EAAGJ,EAAEK,MAAOL,EAAEM,QAClDZ,EAAM,0MAA4MK,GAE/L,SAApBU,EAAqBV,EAAQW,EAAGC,IAE9BC,EADIA,GACWtB,IAAeuB,cAE7BnB,EAAM,qFAKPoB,EAAGC,EAAGC,EAAGC,EAAGC,EAAGC,EAFZC,EAAUrB,EAAOqB,SAAW,GAC/BC,EAAID,EAAQE,OAEND,QACFD,EAAQC,aAAcT,EAAc,CACvCG,EAAIK,EAAQC,YAITN,IACJA,EAAI,IAAIH,EACRQ,EAAQG,KAAKR,GACbhB,EAAOqB,QAAUA,GAElBJ,EAAID,EAAES,QACQ,MAAVd,EAAEe,KACLX,EAAI1B,EAAKsC,MAAMC,WAAWjB,EAAEe,MAC5BR,EAAqB,MAAhBP,EAAEkB,YAAuBlB,EAAEkB,WAAa,EAC7CZ,EAAEa,UAAaf,EAAE,GAAKG,EACtBD,EAAEc,YAAehB,EAAE,GAAKG,EACxBD,EAAEe,WAAcjB,EAAE,GAAKG,EACvBD,EAAEgB,cAAgBhB,EAAEiB,gBAAkBjB,EAAEkB,eAAiB,EAAIjB,WAExDC,KAAKR,EACC,aAANQ,GAA4B,eAANA,IACzBF,EAAEE,IAAMR,EAAEQ,QAIK,MAAdR,EAAEyB,UACLnB,EAAEa,UAAYb,EAAEc,YAAcd,EAAEe,WAAa,KAAQrB,EAAEyB,SAAW,GAClEnB,EAAEgB,cAAgBhB,EAAEiB,gBAAkBjB,EAAEkB,eAAiB,GAC/B,MAAhBxB,EAAE0B,aACZnB,EAAKP,EAAE0B,WAAa,EACpBpB,EAAEa,UAAYb,EAAEc,YAAcd,EAAEe,WAAkB,EAAJd,EAAa,IAAJA,EAAU,EACjED,EAAEgB,cAAgBhB,EAAEiB,gBAAkBjB,EAAEkB,eAAiB,EAAIG,KAAKC,IAAIrB,IAEvEI,EAAI,EACGA,KAEFN,EADJG,EAAIqB,EAAYlB,MACHL,EAAEE,KACdC,EAAKR,EAAO6B,IAAIzB,EAAGG,EAAGH,EAAEG,GAAIF,EAAEE,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,MAEhDC,EAAGsB,GAAK,qBAIX9B,EAAO+B,OAAOnB,KAAK,qBACdxB,EAAO4C,SACX7C,EAAOC,GASM,SAAf6C,EAAgBC,EAAGC,QACZD,aAAaE,OAAYD,aAAcC,cACrCD,MAKP1C,EAAGD,EAHA6C,EAAO,GACV3B,EAAI,EACJ4B,EAAI,MAEA7C,EAAI,EAAGA,EAAI,EAAGA,IAAK,KAClBD,EAAI,EAAGA,EAAI,EAAGA,IAClB8C,EAAW,IAAN9C,EAAW0C,EAAExB,EAAI,GAAK,EAC3B2B,EAAK3B,EAAIlB,GAAK0C,EAAExB,GAAOyB,EAAG3C,GAAK0C,EAAExB,EAAE,GAAKyB,EAAG3C,EAAI,GAAK0C,EAAExB,EAAE,GAAKyB,EAAG3C,EAAI,IAAM0C,EAAExB,EAAE,GAAKyB,EAAG3C,EAAI,IAAM8C,EAEjG5B,GAAK,SAEC2B,EA4CkB,SAA1BE,EAA2BnD,EAAQW,EAAGC,IAEpCwC,EADIA,GACiB7D,IAAe8D,oBAEnC1D,EAAM,qFAKP2D,EAAQC,EAAavC,EAAGwC,EAFrBnC,EAAUrB,EAAOqB,SAAW,GAC/BC,EAAID,EAAQE,QAEC,IAALD,MACJD,EAAQC,aAAc8B,EAAoB,CAC7CpC,EAAIK,EAAQC,aAITN,IACJA,EAAI,IAAIoC,EAAmBK,EAAUC,SACrCrC,EAAQG,KAAKR,GACbhB,EAAOqB,QAAUA,GAElBkC,EAAcvC,EAAEsC,OAChBA,EAASG,EAAUC,QACD,MAAd/C,EAAEgD,WACLL,EAtDU,SAAZM,UAAad,EAAGe,EAAOC,GAClBC,MAAMD,KACTA,EAAS,OAEN/C,EAAI1B,EAAKsC,MAAMC,WAAWiC,GAC7BG,EAAIjD,EAAE,GAAK,IACXkD,EAAIlD,EAAE,GAAK,IACXd,EAAIc,EAAE,GAAK,IACXmD,EAAM,EAAIJ,SACJjB,EAAa,CAACqB,EAAMJ,EAASE,EAAIG,EAAOL,EAASE,EAAII,EAAON,EAASE,EAAIK,EAAO,EAAG,EAAGP,EAASG,EAAIE,EAAOD,EAAMJ,EAASG,EAAIG,EAAON,EAASG,EAAII,EAAO,EAAG,EAAGP,EAAS7D,EAAIkE,EAAOL,EAAS7D,EAAImE,EAAOF,EAAMJ,EAAS7D,EAAIoE,EAAO,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAIvB,GA6CnPc,CAAUN,EAAQ3C,EAAEgD,SAAUW,OAAO3D,EAAE4D,kBAE/B,MAAd5D,EAAE6D,WACLlB,EAnCa,SAAfmB,aAAgB3B,EAAG4B,UACdX,MAAMW,GACF5B,EAGDD,EAAa,CADpB6B,GAAK,IACkB,EAAE,EAAE,EAAE,KAAO,EAAIA,GAAI,EAAEA,EAAE,EAAE,EAAE,KAAO,EAAIA,GAAI,EAAE,EAAEA,EAAE,EAAE,KAAO,EAAIA,GAAI,EAAE,EAAE,EAAE,EAAE,GAAI5B,GA8B5F2B,CAAanB,EAAQgB,OAAO3D,EAAE6D,YAE3B,MAAT7D,EAAEgE,MACLrB,EAhDQ,SAAVsB,QAAW9B,EAAG4B,MACTX,MAAMW,UACF5B,EAER4B,GAAKpC,KAAKuC,GAAK,QACX9D,EAAIuB,KAAKwC,IAAIJ,GAChB1D,EAAIsB,KAAKyC,IAAIL,UACP7B,EAAa,CAAEsB,EAASpD,GAAK,EAAIoD,GAAYnD,GAAMmD,EAAUC,EAASrD,GAAMqD,EAAYpD,GAAMoD,EAAUC,EAAStD,GAAMsD,EAAYrD,GAAK,EAAIqD,GAAS,EAAG,EAAIF,EAASpD,GAAMoD,EAAgB,KAAJnD,EAAaoD,EAASrD,GAAK,EAAIqD,GAAgB,IAAJpD,EAAYqD,EAAStD,GAAMsD,GAAiB,KAALrD,EAAa,EAAG,EAAImD,EAASpD,GAAMoD,EAAYnD,IAAO,EAAImD,GAAWC,EAASrD,GAAMqD,EAAYpD,EAAIoD,EAASC,EAAStD,GAAK,EAAIsD,GAAYrD,EAAIqD,EAAQ,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAIvB,GAyClc8B,CAAQtB,EAAQgB,OAAO3D,EAAEgE,OAEf,MAAhBhE,EAAEqE,aACL1B,EA1Ee,SAAjB2B,eAAkBnC,EAAG4B,MAChBX,MAAMW,UACF5B,MAEJoB,EAAM,EAAIQ,EACbV,EAAIE,EAAMC,EACVF,EAAIC,EAAME,EACVnE,EAAIiE,EAAMG,SACJxB,EAAa,CAACmB,EAAIU,EAAGT,EAAGhE,EAAG,EAAG,EAAG+D,EAAGC,EAAIS,EAAGzE,EAAG,EAAG,EAAG+D,EAAGC,EAAGhE,EAAIyE,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAI5B,GAkEpFmC,CAAe3B,EAAQgB,OAAO3D,EAAEqE,cAG1C1D,EAAIgC,EAAO/B,QACG,IAALD,GACJgC,EAAOhC,KAAOiC,EAAYjC,KAC7BkC,EAAK5C,EAAO6B,IAAIc,EAAajC,EAAGiC,EAAYjC,GAAIgC,EAAOhC,GAAI,EAAG,EAAG,EAAG,EAAG,EAAG,MAEzEkC,EAAGd,GAAK,2BAKX9B,EAAO+B,OAAOnB,KAAK,2BACdxB,EAAO4C,SACX7C,IAGDa,EAAOsE,QAAU3B,EAGN,SAAZ4B,EAAYC,GACX/F,EAAO+F,GAAQhG,IACXF,MACHO,EAAON,QAEJE,IAEHgG,EAAe,GAxMlB,IAAIhG,EAAMgG,EAAc5F,EAAMD,EAAWqB,EAAcuC,EACtDZ,EAAc,4GAA4G8C,MAAM,KA0EhI7B,EAAY,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GACnDU,EAAQ,QACRC,EAAQ,OACRC,EAAQ,QA+HIkB,EAAc,CAC1BC,QAAS,SACTC,KAAM,QACNC,mBAAK1F,EAAQ2F,EAAOC,EAAOC,EAAOC,OAQ7B3E,EAAGC,EAAIM,EAAMqE,EAAaC,EAAKC,EAAQ3E,MACtCH,KARAkE,IACJF,IACK9F,GACJM,EAAM,iDAGHK,OAASA,EAEJ2F,KAETK,EAAML,EAAMxE,GACF,gBAANA,GAA6B,SAANA,GAAsB,eAANA,GAA4B,aAANA,GAA0B,eAANA,EAC/EO,IACJhB,EAAkBV,EAAQ2F,EAAMO,aAAeP,EAAOQ,MACtDzE,GAAO,QAGF,GAAU,eAANP,GAA4B,aAANA,GAA0B,QAANA,GAAqB,aAANA,GAA0B,mBAANA,EAClF4E,IACJ5C,EAAwBnD,EAAQ2F,EAAMS,mBAAqBT,EAAOQ,MAClEJ,GAAc,QAGT,GAAU,UAAN5E,EAAe,IACL,iBAAT6E,GAAuC,MAAlBA,EAAIK,OAAO,KAAeJ,EAASjG,EAAOiG,YACpE3E,EAAI,EAAGA,EAAI2E,EAAO1E,OAAQD,IAC1B2E,EAAO3E,GAAGgF,QAAUN,IACvBA,EAAMC,EAAO3E,GAAGiF,WAInBnF,EAAK+E,KAAK1D,IAAIzC,EAAQ,cAAeA,EAAOwG,aAAcR,EAAKH,EAAOC,EAASxD,KAAKmE,MAAO,EAAG,EAAG,MAEhGrF,EAAGsB,GAAKvB,QAGc,MAAbnB,EAAOmB,SACZsB,IAAIzC,EAAQmB,EAAG,MAAO6E,IAK9BU,uBAAOC,EAAOC,WACTxF,EAAKwF,EAAKC,IACPzF,GACNA,EAAG4C,EAAE2C,EAAOvF,EAAG0F,GACf1F,EAAKA,EAAG2F,MAELH,EAAK5G,OAAO4C,SACfgE,EAAK5G,OAAOgH,eAGdC,SAAU9B,EAGXI,iBAA+B,SAAA7F,GAC9BF,EAAYE,IAGbN,KAAcC,EAAKC,eAAeiG"}