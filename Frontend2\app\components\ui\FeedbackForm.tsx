import { POPUP_STYLES } from "~/lib/utils";
import { useState } from "react";
import type { FeedbackFormData, FollowUpConsent } from "~/lib/types";
import { RATING_SCALE, RATING_LABELS, FOLLOW_UP_CONSENT_OPTIONS } from "~/lib/types";
import { useFeedbackSubmission } from "~/lib/hooks";

interface FeedbackFormProps {
  onClose: () => void;
  onSuccess?: () => void;
}

export function FeedbackForm({ onClose, onSuccess }: FeedbackFormProps) {
  const [formData, setFormData] = useState<FeedbackFormData>({
    user_email: '',
    visual_design_rating: undefined,
    visual_design_comments: '',
    ease_of_navigation_rating: undefined,
    ease_of_navigation_comments: '',
    mobile_responsiveness_rating: undefined,
    mobile_responsiveness_comments: '',
    overall_satisfaction_rating: undefined,
    overall_satisfaction_comments: '',
    task_completion_rating: undefined,
    task_completion_comments: '',
    service_quality_rating: undefined,
    service_quality_comments: '',
    liked_features: '',
    improvement_suggestions: '',
    desired_features: '',
    legal_challenges: '',
    additional_feedback: '',
    follow_up_consent: 'no',
    follow_up_email: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const { submit, isSubmitting, error, success, successMessage, reset } = useFeedbackSubmission();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;

    let processedValue: any = value;

    // Handle radio buttons for ratings
    if (type === 'radio' && name.includes('_rating')) {
      processedValue = parseInt(value);
    }

    // Handle empty strings for optional fields
    if (value === '' && !name.includes('_rating')) {
      processedValue = undefined;
    }

    setFormData(prev => ({
      ...prev,
      [name]: processedValue
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.user_email?.trim()) {
      newErrors.user_email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.user_email)) {
      newErrors.user_email = 'Please enter a valid email address';
    }

    // Validate follow-up email if consent is given
    if (formData.follow_up_consent === 'yes' && !formData.follow_up_email?.trim()) {
      newErrors.follow_up_email = 'Follow-up email is required when consent is given';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const success = await submit(formData);
    if (success) {
      onSuccess?.();
      // Keep form open to show success message
    }
  };

  // Handle closing after success
  const handleClose = () => {
    reset();
    onClose();
  };

  const renderRatingQuestion = (
    name: string,
    label: string,
    questionNumber: number,
    commentsName?: string
  ) => (
    <div className="mb-6">
      <label className="block font-medium text-sm text-white/90 mb-3 font-montserrat drop-shadow-sm">
        {questionNumber}. {label}
      </label>
      <div className="flex gap-4 flex-wrap mb-3">
        {RATING_SCALE.map(num => (
          <label key={num} className="flex items-center gap-1 cursor-pointer text-sm text-white/80 hover:text-white transition-colors bg-white/5 backdrop-blur-sm rounded-lg px-2 py-1 border border-white/10 hover:bg-white/10">
            <input
              type="radio"
              name={name}
              value={num.toString()}
              checked={formData[name as keyof FeedbackFormData] === num}
              onChange={handleInputChange}
              className="w-4 h-4 accent-law-gold"
            />
            <span className="font-medium">{num} - {RATING_LABELS[num as keyof typeof RATING_LABELS]}</span>
          </label>
        ))}
      </div>
      {commentsName && (
        <div className="mt-3">
          <label htmlFor={commentsName} className="block text-sm text-white/80 mb-2 font-montserrat drop-shadow-sm">
            Comments (Optional):
          </label>
          <textarea
            id={commentsName}
            name={commentsName}
            value={formData[commentsName as keyof FeedbackFormData] as string || ''}
            onChange={handleInputChange}
            rows={2}
            className={POPUP_STYLES.textarea}
            placeholder="Share your thoughts..."
          />
        </div>
      )}
    </div>
  );

  return (
    <div className="w-full">
      <h2 className={POPUP_STYLES.title}>
        Help us improve your experience
      </h2>
      
      <p className="text-center text-gray-600 text-sm mb-3 font-source-sans-pro">
        <em>Estimated time: 5-7 minutes</em>
      </p>
      
      <p className="text-sm text-gray-600 mb-8 text-center leading-relaxed font-source-sans-pro">
        Thank you for taking the time to share your feedback. Your input helps us enhance our website to better serve your legal needs. All responses are confidential and used solely to improve our platform.
      </p>

      <form onSubmit={handleSubmit} className="flex flex-col gap-8">
        {/* Email Field */}
        <div className={POPUP_STYLES.formGroup}>
          <label htmlFor="user_email" className={POPUP_STYLES.label}>
            Email Address *
          </label>
          <input
            type="email"
            id="user_email"
            name="user_email"
            value={formData.user_email || ''}
            onChange={handleInputChange}
            className={`${POPUP_STYLES.input} ${errors.user_email ? 'border-red-500' : ''}`}
            placeholder="Enter your email address"
            required
          />
          {errors.user_email && (
            <p className="text-red-500 text-sm mt-1">{errors.user_email}</p>
          )}
        </div>

        {/* User Interface Section */}
        <div className="border-b border-white/20 pb-6 mb-6">
          <h3 className="bg-gradient-to-r from-law-gold via-yellow-300 to-law-gold bg-clip-text text-transparent font-merriweather font-bold text-xl mb-3 drop-shadow-lg">
            User Interface
          </h3>
          <p className="text-sm text-white/80 mb-5 font-source-sans-pro drop-shadow-sm">
            Please rate the following on a scale of 1 to 5 (1 = Poor, 5 = Excellent):
          </p>

          {renderRatingQuestion('visual_design_rating', 'Visual design and layout', 1, 'visual_design_comments')}
          {renderRatingQuestion('ease_of_navigation_rating', 'Ease of navigation', 2, 'ease_of_navigation_comments')}
          {renderRatingQuestion('mobile_responsiveness_rating', 'Mobile responsiveness', 3, 'mobile_responsiveness_comments')}
        </div>

        {/* User Experience Section */}
        <div className="border-b border-slate-600/30 pb-6">
          <h3 className="bg-gradient-to-r from-law-gold via-yellow-400 to-law-gold bg-clip-text text-transparent font-merriweather font-bold text-xl mb-3">
            User Experience
          </h3>
          <p className="text-sm text-gray-300 mb-5 font-source-sans-pro">
            Please rate the following on a scale of 1 to 5 (1 = Poor, 5 = Excellent):
          </p>

          {renderRatingQuestion('overall_satisfaction_rating', 'Overall satisfaction with the website', 4, 'overall_satisfaction_comments')}
          {renderRatingQuestion('task_completion_rating', 'Ease of completing tasks (e.g., finding information, using tools)', 5, 'task_completion_comments')}
          {renderRatingQuestion('service_quality_rating', 'Quality of services provided', 6, 'service_quality_comments')}
        </div>

        {/* Suggestions Section */}
        <div className="border-b border-gray-200 pb-6">
          <h3 className="bg-gold-texture bg-clip-text text-transparent font-merriweather font-bold text-xl mb-5 bg-cover bg-center">
            Suggestions and Legal Needs
          </h3>

          <div className={POPUP_STYLES.formGroup}>
            <label htmlFor="liked_features" className={POPUP_STYLES.label}>
              7. What do you like most about our website?
            </label>
            <textarea
              id="liked_features"
              name="liked_features"
              value={formData.liked_features || ''}
              onChange={handleInputChange}
              rows={3}
              className={POPUP_STYLES.textarea}
              placeholder="Tell us what you enjoyed..."
            />
          </div>

          <div className={POPUP_STYLES.formGroup}>
            <label htmlFor="improvement_suggestions" className={POPUP_STYLES.label}>
              8. What improvements would you suggest for our website?
            </label>
            <textarea
              id="improvement_suggestions"
              name="improvement_suggestions"
              value={formData.improvement_suggestions || ''}
              onChange={handleInputChange}
              rows={3}
              className={POPUP_STYLES.textarea}
              placeholder="Share your suggestions..."
            />
          </div>

          <div className={POPUP_STYLES.formGroup}>
            <label htmlFor="desired_features" className={POPUP_STYLES.label}>
              9. Are there any features you would like us to add?
            </label>
            <textarea
              id="desired_features"
              name="desired_features"
              value={formData.desired_features || ''}
              onChange={handleInputChange}
              rows={3}
              className={POPUP_STYLES.textarea}
              placeholder="Describe features you'd like to see..."
            />
          </div>

          <div className={POPUP_STYLES.formGroup}>
            <label htmlFor="legal_challenges" className={POPUP_STYLES.label}>
              10. What legal challenges are you facing that our website could help address?
            </label>
            <textarea
              id="legal_challenges"
              name="legal_challenges"
              value={formData.legal_challenges || ''}
              onChange={handleInputChange}
              rows={3}
              className={POPUP_STYLES.textarea}
              placeholder="Tell us about your legal needs..."
            />
          </div>
        </div>

        {/* Additional Comments Section */}
        <div className="border-b border-gray-200 pb-6">
          <h3 className="bg-gold-texture bg-clip-text text-transparent font-merriweather font-bold text-xl mb-5 bg-cover bg-center">
            Additional Comments
          </h3>

          <div className={POPUP_STYLES.formGroup}>
            <label htmlFor="additional_feedback" className={POPUP_STYLES.label}>
              11. Is there anything else you would like to share?
            </label>
            <textarea
              id="additional_feedback"
              name="additional_feedback"
              value={formData.additional_feedback || ''}
              onChange={handleInputChange}
              rows={3}
              className={POPUP_STYLES.textarea}
              placeholder="Any additional thoughts or feedback..."
            />
          </div>
        </div>

        {/* Follow-up Section */}
        <div>
          <h3 className="bg-gold-texture bg-clip-text text-transparent font-merriweather font-bold text-xl mb-5 bg-cover bg-center">
            Follow-up Consent
          </h3>

          <div className={POPUP_STYLES.formGroup}>
            <label className="block font-medium text-base text-gray-800 mb-3">
              12. Do you consent to follow-up contact regarding your feedback?
            </label>
            <div className="flex flex-col gap-3">
              {FOLLOW_UP_CONSENT_OPTIONS.map(option => (
                <label key={option.value} className="flex items-center gap-2 cursor-pointer text-sm text-gray-600">
                  <input
                    type="radio"
                    name="follow_up_consent"
                    value={option.value}
                    checked={formData.follow_up_consent === option.value}
                    onChange={handleInputChange}
                    className="w-4 h-4 accent-law-gold"
                  />
                  <span>{option.label}</span>
                </label>
              ))}
            </div>
          </div>

          {formData.follow_up_consent === 'yes' && (
            <div className={POPUP_STYLES.formGroup}>
              <label htmlFor="follow_up_email" className={POPUP_STYLES.label}>
                Follow-up Email Address *
              </label>
              <input
                type="email"
                id="follow_up_email"
                name="follow_up_email"
                value={formData.follow_up_email || ''}
                onChange={handleInputChange}
                className={`${POPUP_STYLES.input} ${errors.follow_up_email ? 'border-red-500' : ''}`}
                placeholder="Enter email for follow-up contact"
                required={formData.follow_up_consent === 'yes'}
              />
              {errors.follow_up_email && (
                <p className="text-red-500 text-sm mt-1">{errors.follow_up_email}</p>
              )}
            </div>
          )}
        </div>

        {/* Success Message */}
        {success && successMessage && (
          <div className="bg-green-50 text-green-800 p-4 rounded-lg border border-green-200 text-center">
            <p className="font-medium">{successMessage}</p>
            <button
              type="button"
              onClick={handleClose}
              className="mt-2 text-sm text-green-600 hover:text-green-800 underline"
            >
              Close
            </button>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 text-red-800 p-4 rounded-lg border border-red-200 text-center">
            <p>{error}</p>
          </div>
        )}

        {!success && (
          <button
            type="submit"
            className={`${POPUP_STYLES.button} ${isSubmitting ? 'opacity-60 cursor-not-allowed' : ''}`}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
          </button>
        )}
      </form>
    </div>
  );
}

export default FeedbackForm;
